<?php
// Sample JSON data
$json = '{
  "data": {
    "tcs": [
      {
        "samt": 0,
        "flag": "N",
        "amt": 3066.1,
        "camt": 0,
        "iamt": 15.34,
        "retsupU": 0,
        "supU": 3066.1,
        "supR": 0,
        "month": "042025",
        "pos": "01",
        "ctin": "27AACCT7290E1CI",
        "retsupR": 0,
        "chksum": "5d702343cdd7dde5a24416560b2098738dcf4cc96cc1242ec1ea2b6933bd02cb"
      },
      // ... (rest of JSON data remains the same)
    ],
    "req_time": "15-05-2025 13:10"
  },
  "status_cd": "1",
  "status_desc": "GSTR request succeeds",
  "header": {
    "content-type": "application/json",
    "gst_username": "ABEESMART",
    "state_cd": "27",
    "ip_address": "************",
    "client_id": "l7xx07b08bb77aa143e6a036e32a95863354",
    "client_secret": "c4f4cb1b39634极d7db01ba2b34287a7a7",
    "txn": "2682315dd416433cb1d76496976cb3bd",
    "ret_period": "042025",
    "gstin": "27AASCA0636R1ZZ"
  }
}';

$data = json_decode($json, true);
$summary = [];
$breakup = [];

// Process each TCS entry
foreach ($data['data']['tcs'] as $entry) {
    $ctin = $entry['ctin'];
    $pos = $entry['pos'];
    $key = $ctin . '_' . $pos;
    
    // Initialize summary
    if (!isset($summary[$ctin])) {
        $summary[$ctin] = [
            'taxable_sales' => 0,
            'taxable_return' => 0,
            'taxable_value' => 0,
            'sgst_amount' => 0,
            'cgst_amount' => 0,
            'igst_amount' => 0
        ];
    }
    
    // Initialize breakup
    if (!isset($breakup[$key])) {
        $breakup[$key] = [
            'pos' => $pos,
            'taxable_sales' => 0,
            'taxable_return' => 0,
            'taxable_value' => 0,
            'sgst_amount' => 0,
            'cgst_amount' => 0,
            'igst_amount' => 0
        ];
    }
    
    // Update summary
    $summary[$ctin]['taxable_sales'] += bcadd($entry['supU'], $entry['supR'], 2);
    $summary[$ctin]['taxable_return'] += bcadd($entry['retsupU'], $entry['retsupR'], 2);
    $summary[$ctin]['taxable_value'] += round($entry['amt'], 2, PHP_ROUND_HALF_UP);
    $summary[$ctin]['sgst_amount'] += round($entry['samt'], 2, PHP_ROUND_HALF_UP);
    $summary[$ctin]['cgst_amount'] += round($entry['camt'], 2, PHP_ROUND_HALF_UP);
    $summary[$ctin]['igst_amount'] += round($entry['iamt'], 2, PHP_ROUND_HALF_UP);
    
    // Update breakup
    $breakup[$key]['taxable_sales'] += bcadd($entry['supU'], $entry['supR'], 2);
    $breakup[$key]['taxable_return'] += bcadd($entry['retsupU'], $entry['retsupR'], 2);
    $breakup[$key]['taxable_value'] += round($entry['amt'], 2, PHP_ROUND_HALF_UP);
    $breakup[$key]['sgst_amount'] += round($entry['samt'], 2, PHP_ROUND_HALF_UP);
    $breakup[$key]['cgst_amount'] += round($entry['camt'], 2, PHP_ROUND_HALF_UP);
    $breakup[$key]['igst_amount'] += round($entry['iamt'], 2, PHP_ROUND_HALF_UP);
}

// Output the summary
echo "<h2>CTIN-wise Summary</h2>";
echo "<table border='1'>";
echo "<tr><th>CTIN</th><th>Taxable Sales</th><th>Taxable Return</th><th>Taxable Value</th><th>SGST</th><th>CGST</th><th>IGST</th></tr>";
foreach ($summary as $ctin => $details) {
    echo "<tr>";
    echo "<td>$ctin</td>";
    echo "<td>" . $this->currency->format($details['taxable_sales']) . "</td>";
    echo "<td>" . $this->currency->format($details['taxable_return']) . "</td>";
    echo "<td>" . $this->currency->format($details['taxable_value']) . "</td>";
    echo "<td>" . $this->currency->format($details['sgst_amount']) . "</td>";
    echo "<td>" . $this->currency->format($details['cgst_amount']) . "</td>";
    echo "<td>" . $this->currency->format($details['igst_amount']) . "</td>";
    echo "</tr>";
}
echo "</table>";

// Output the breakup
echo "<h2>CTIN and POS-wise Breakup</h2>";
echo "<table border='1'>";
echo "<tr><th>CTIN</th><th>POS</th><th>Taxable Sales</th><th>Taxable Return</th><th>Taxable Value</th><th>SGST</th><th>CGST</th><th>IGST</th></tr>";
foreach ($breakup as $key => $details) {
    list($ctin, $pos) = explode('_', $key);
    echo "<tr>";
    echo "<td>$ctin</td>";
    echo "<td>$pos</td>";
    echo "<td>" . $this->currency->format($details['taxable_sales']) . "</td>";
    echo "<td>" . $this->currency->format($details['taxable_return']) . "</td>";
    echo "<td>" . $this->currency->format($details['taxable_value']) . "</td>";
    echo "<td>" . $this->currency->format($details['sgst_amount']) . "</td>";
    echo "<td>" . $this->currency->format($details['cgst_amount']) . "</td>";
    echo "<td>" . $this->currency->format($details['igst_amount']) . "</td>";
    echo "</tr>";
}
echo "</table>";
?>


