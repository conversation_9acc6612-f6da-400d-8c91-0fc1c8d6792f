<div class="col-md-12 fl">
	<div class="fl width100 text-center h3"><?php echo $repot_header['report_name']; ?></div>
	<div class="fl width100 text-center h4"><?php echo $repot_header['company_name']; ?></div>
	<div class="fl width100 text-center sbold"><?php echo $repot_header['address']; ?></div>
	<div class="fl width100 text-center sbold"><?php echo $repot_header['date_range']; ?></div>
</div>

$json =''{
	"aacct7290e": {
	  "channel_name": "Tata Cliq Cus",
	  "flag": "A",
	  "flag_message": "<span class=\"text-primary strong6\">Accepted</span>",
	  "month": "Apr-2025",
	  "gst_number": "27AACCT7290E1CI",
	  "chksum": "c1db220e08903d0b133bc70544ce2eefc6d6688069fee5813787a37e56c1ca23",
	  "data_row": 2,
	  "gstn": {
		"type": "GSTN",
		"taxable_sales": "₹9,184.<small>00</small>",
		"taxable_return": "₹618.<small>75</small>",
		"taxable_value": "₹40,967.<small>28</small>",
		"sgst_amount": "₹12.<small>80</small>",
		"cgst_amount": "₹12.<small>80</small>",
		"igst_amount": "₹179.<small>33</small>"
	  },
	  "upload": {
		"type": "DATA",
		"taxable_sales": "₹49,899.<small>45</small>",
		"taxable_return": "₹9,008.<small>44</small>",
		"taxable_value": "₹40,891.<small>01</small>",
		"sgst_amount": "₹12.<small>80</small>",
		"cgst_amount": "₹12.<small>80</small>",
		"igst_amount": "₹178.<small>86</small>",
		"state_wise_data": [
		  {
			"ship_to_state": "Andhra Pradesh",
			"taxable_value": "8565.25",
			"taxable_sales": 9184,
			"taxable_return": 618.75,
			"igst_amount": "42.80",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "Delhi",
			"taxable_value": "854.24",
			"taxable_sales": 854.24,
			"taxable_return": 0,
			"igst_amount": "4.28",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "Gujarat",
			"taxable_value": "2386.61",
			"taxable_sales": 2386.61,
			"taxable_return": 0,
			"igst_amount": "11.94",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "Haryana",
			"taxable_value": "648.31",
			"taxable_sales": 1395.63,
			"taxable_return": 747.32,
			"igst_amount": "3.24",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "Jammu & Kashmir",
			"taxable_value": "3066.10",
			"taxable_sales": 3066.1,
			"taxable_return": 0,
			"igst_amount": "15.34",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "Jharkhand",
			"taxable_value": "3645.76",
			"taxable_sales": 5140.68,
			"taxable_return": 1494.92,
			"igst_amount": "18.24",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "Karnataka",
			"taxable_value": "46.72",
			"taxable_sales": 2128.92,
			"taxable_return": 2082.2,
			"igst_amount": "0.22",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "Kerala",
			"taxable_value": "1624.57",
			"taxable_sales": 1624.57,
			"taxable_return": 0,
			"igst_amount": "8.12",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "Maharashtra",
			"taxable_value": "5118.04",
			"taxable_sales": 5118.04,
			"taxable_return": 0,
			"igst_amount": "0.00",
			"cgst_amount": "12.80",
			"sgst_amount": "12.80"
		  },
		  {
			"ship_to_state": "Nagaland",
			"taxable_value": "2127.97",
			"taxable_sales": 2127.97,
			"taxable_return": 0,
			"igst_amount": "10.64",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "Odisha",
			"taxable_value": "618.75",
			"taxable_sales": 1114.51,
			"taxable_return": 495.76,
			"igst_amount": "3.10",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "Punjab",
			"taxable_value": "1937.56",
			"taxable_sales": 1937.56,
			"taxable_return": 0,
			"igst_amount": "9.68",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "Rajasthan",
			"taxable_value": "1406.25",
			"taxable_sales": 2840.15,
			"taxable_return": 1433.9,
			"igst_amount": "7.04",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "Tamil Nadu",
			"taxable_value": "-76.27",
			"taxable_sales": 633.05,
			"taxable_return": 709.32,
			"igst_amount": "-0.38",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "Telangana",
			"taxable_value": "2700.95",
			"taxable_sales": 4127.22,
			"taxable_return": 1426.27,
			"igst_amount": "13.50",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "Uttar Pradesh",
			"taxable_value": "4595.62",
			"taxable_sales": 4595.62,
			"taxable_return": 0,
			"igst_amount": "22.98",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  },
		  {
			"ship_to_state": "West Bengal",
			"taxable_value": "1624.58",
			"taxable_sales": 1624.58,
			"taxable_return": 0,
			"igst_amount": "8.12",
			"cgst_amount": "0.00",
			"sgst_amount": "0.00"
		  }
		]
	  },
	  "issue": {
		"taxable_sales": true,
		"taxable_return": true,
		"taxable_value": true,
		"sgst_amount": true,
		"cgst_amount": true,
		"igst_amount": true
	  },
	  "data": "{\"gst_number\":\"27AACCT7290E1CI\",\"month\":\"042025\",\"chksum\":\"c1db220e08903d0b133bc70544ce2eefc6d6688069fee5813787a37e56c1ca23\"}"
	},;


<?php if($tcs_data) { ?>
<div class="fl col-md-12 margint50 strong6 font130 text-center" style="display: none;"><?php echo $text_gstr2x_tcs_credit_received; ?></div>

<div class="fl table-scrollable table-responsive margint20">
	<table class="table table-bordered" id="gstr2x_tcs_table">
		<thead>
			<tr>
				<th class="text-center"><?php echo $column_gstn; ?></th>
				<th class="text-center"><?php echo $column_tax_period; ?></th>
				<th class="text-center"></th>
				<th class="text-center"><?php echo $column_supplies_sales; ?></th>
				<th class="text-center"><?php echo $column_supplies_return; ?></th>
				<th class="text-center"><?php echo $column_supplies_net_value; ?></th>
				<th class="text-center"><?php echo $column_tcs_igst; ?></th>
				<th class="text-center"><?php echo $column_tcs_cgst; ?></th>
				<th class="text-center"><?php echo $column_tcs_sgst; ?></th>
				<th class="text-right"><?php echo $column_action; ?></th>
			</tr>
		</thead>
		<tbody>
			<?php foreach($tcs_data AS $pan_number => $datas) { ?>
				<?php if($datas['data_row'] == 2) { $pan_number_array[] = $pan_number; } ?>
				<tr class="main bgcolorf9f9f9 bg-font-default" id="<?php echo $datas['chksum']; ?>" data-ctin="<?php echo $datas['gst_number']; ?>">
					<td rowspan="<?php echo $datas['data_row']; ?>" class="text-center">
						<i class="fa fa-plus-circle toggle-breakup" data-ctin="<?php echo $datas['gst_number']; ?>"></i>

						<?php echo $datas['channel_name']; ?><br />
						<?php echo $datas['gst_number']; ?>
					</td>
					
					<td rowspan="<?php echo $datas['data_row']; ?>" class="text-center"><?php echo $datas['month']; ?></td>
					<td class="text-center"><?php echo $datas['gstn']['type']; ?></td>
					<td class="text-center"><?php echo $datas['gstn']['taxable_sales']; ?></td>
					<td class="text-center"><?php echo $datas['gstn']['taxable_return']; ?></td>
					<td class="text-center"><?php echo $datas['gstn']['taxable_value']; ?></td>
					<td class="text-center"><?php echo $datas['gstn']['igst_amount']; ?></td>
					<td class="text-center"><?php echo $datas['gstn']['cgst_amount']; ?></td>
					<td class="text-center"><?php echo $datas['gstn']['sgst_amount']; ?></td>
					<td rowspan="<?php echo $datas['data_row']; ?>" class="text-right">
						<?php if($datas['flag'] == 'N'){ ?>
							<button onclick="updateGstr2x('A','<?php echo $datas['chksum']; ?>')" class="btn btn-sm btn-outline green accept"><?php echo $button_accept; ?></button>
							<button onclick="updateGstr2x('R','<?php echo $datas['chksum']; ?>')" class="btn btn-sm btn-outline red reject"><?php echo $button_reject; ?></button>
						<?php } else { ?>
							<?php echo $datas['flag_message']; ?>
						<?php } ?>
					</td>
				</tr>


				<?php if(isset($datas['upload'])){ ?>
					<tr class="main bgcolorf9f9f9 bg-font-default">
						<td class="text-center"><?php echo $datas['upload']['type']; ?></td>
						<td class="text-center <?php echo $datas['issue']['taxable_sales'] ? 'danger' : ''; ?>"><?php echo $datas['upload']['taxable_sales']; ?></td>
						<td class="text-center <?php echo $datas['issue']['taxable_return'] ? 'danger' : ''; ?>"><?php echo $datas['upload']['taxable_return']; ?></td>
						<td class="text-center <?php echo $datas['issue']['taxable_value'] ? 'danger' : ''; ?>"><?php echo $datas['upload']['taxable_value']; ?></td>
						<td class="text-center <?php echo $datas['issue']['igst_amount'] ? 'danger' : ''; ?>"><?php echo $datas['upload']['igst_amount']; ?></td>
						<td class="text-center <?php echo $datas['issue']['cgst_amount'] ? 'danger' : ''; ?>"><?php echo $datas['upload']['cgst_amount']; ?></td>
						<td class="text-center <?php echo $datas['issue']['sgst_amount'] ? 'danger' : ''; ?>"><?php echo $datas['upload']['sgst_amount']; ?></td>
					</tr>

					
					<tr class="state-wise-data">
						<td colspan="3" class="text-center">State-wise Breakdown</td>
						<td class="text-center"><?php echo $datas['upload']['taxable_sales']; ?></td>
						<td class="text-center"><?php echo $datas['upload']['taxable_return']; ?></td>
						<td class="text-center"><?php echo $datas['upload']['taxable_value']; ?></td>
						<td class="text-center"><?php echo $datas['upload']['igst_amount']; ?></td>
						<td class="text-center"><?php echo $datas['upload']['cgst_amount']; ?></td>
						<td class="text-center"><?php echo $datas['upload']['sgst_amount']; ?></td>
						<td></td>
					</tr>
					<?php if(isset($datas['upload']['state_wise_data'])) { ?>
						<?php foreach($datas['upload']['state_wise_data'] as $state_data) { ?>
							<tr class="state-wise-details">
								<td colspan="3" class="text-center"><?php echo $state_data['ship_to_state']; ?></td>
								<td class="text-center"><?php echo $state_data['taxable_sales']; ?></td>
								<td class="text-center"><?php echo $state_data['taxable_return']; ?></td>
								<td class="text-center"><?php echo $state_data['taxable_value']; ?></td>
								<td class="text-center"><?php echo $state_data['igst_amount']; ?></td>
								<td class="text-center"><?php echo $state_data['cgst_amount']; ?></td>
								<td class="text-center"><?php echo $state_data['sgst_amount']; ?></td>
								<td></td>
							</tr>
						<?php } ?>
					<?php } ?>


				<?php } ?>
			<?php } ?>
		</tbody>
	</table>
</div>
<?php } ?>

 <!-- CTIN and POS-wise Breakup (Initially Hidden) -->
      <?php if ($tcs_data_pos_ctin) { ?>
        <?php 
        // Group tcs_data_pos_ctin by CTIN
        $breakup_by_ctin = array();
        foreach ($tcs_data_pos_ctin as $key => $details) {
          list($ctin, $pos) = explode('_', $key);
          if (!isset($breakup_by_ctin[$ctin])) {
            $breakup_by_ctin[$ctin] = array();
          }
          $breakup_by_ctin[$ctin][$key] = $details;
        }
        ?>
        
        <!-- These containers will be used as templates and not displayed directly -->
        <div style="display: none;">
          <?php foreach ($breakup_by_ctin as $ctin => $ctin_breakups) { ?>
            <div class="breakup-container" id="breakup-<?php echo $ctin; ?>">
              <div class="card">
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                      <thead>
                        <tr>
                          <th class="text-center">State</th>
                          <th class="text-right">Sales</th>
                          <th class="text-right">Returned</th>
                          <th class="text-right">Net</th>
                          <th class="text-right">IGST</th>
                          <th class="text-right">CGST</th>
                          <th class="text-right">SGST</th>
                        </tr>
                      </thead>
                      <tbody>
                        <?php 
                        // Initialize totals
                        $total_sales = 0;
                        $total_return = 0;
                        $total_value = 0;
                        $total_igst = 0;
                        $total_cgst = 0;
                        $total_sgst = 0;
                        
                        foreach ($ctin_breakups as $key => $details) { 
                          // Accumulate totals
                          $total_sales += $details['taxable_sales'];
                          $total_return += $details['taxable_return'];
                          $total_value += $details['taxable_value'];
                          $total_igst += $details['igst_amount'];
                          $total_cgst += $details['cgst_amount'];
                          $total_sgst += $details['sgst_amount'];
                        ?>
                          <tr>
                            <td class="text-center"><?php echo $details['pos']; ?></td>
                            <td class="text-right"><?php echo number_format($details['taxable_sales'], 2); ?></td>
                            <td class="text-right"><?php echo number_format($details['taxable_return'], 2); ?></td>
                            <td class="text-right"><?php echo number_format($details['taxable_value'], 2); ?></td>
                            <td class="text-right"><?php echo number_format($details['igst_amount'], 2); ?></td>
                            <td class="text-right"><?php echo number_format($details['cgst_amount'], 2); ?></td>
                            <td class="text-right"><?php echo number_format($details['sgst_amount'], 2); ?></td>
                          </tr>
                        <?php } ?>
                        <!-- Add totals row -->
                        <tr class="total-row">
                          <td class="text-right">Total:</td>
                          <td class="text-right"><?php echo number_format($total_sales, 2); ?></td>
                          <td class="text-right"><?php echo number_format($total_return, 2); ?></td>
                          <td class="text-right"><?php echo number_format($total_value, 2); ?></td>
                          <td class="text-right"><?php echo number_format($total_igst, 2); ?></td>
                          <td class="text-right"><?php echo number_format($total_cgst, 2); ?></td>
                          <td class="text-right"><?php echo number_format($total_sgst, 2); ?></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          <?php } ?>
        </div>
      <?php } ?>

<?php if($tcsa_data) { ?>
<div class="fl col-md-12 margint50 strong6 font130 text-center"><?php echo $text_gstr2x_amendments_tcs_credit_received; ?></div>

<div class="fl table-scrollable table-responsive margint20">
	<table class="table table-bordered" id="gstr2x_tcsa_table">
		<thead>
			<tr>
				<th class="text-center"><?php echo $column_gstn; ?></th>
				<th class="text-center"><?php echo $column_original_tax_period; ?></th>
				<th class="text-center"></th>
				<th class="text-center"><?php echo $column_supplies_sales; ?></th>
				<th class="text-center"><?php echo $column_supplies_return; ?></th>
				<th class="text-center"><?php echo $column_supplies_net_value; ?></th>
				<th class="text-center"><?php echo $column_tcs_igst; ?></th>
				<th class="text-center"><?php echo $column_tcs_cgst; ?></th>
				<th class="text-center"><?php echo $column_tcs_sgst; ?></th>
				<th class="text-right"><?php echo $column_action; ?></th>
			</tr>
		</thead>
		<tbody>
			<?php foreach($tcsa_data AS $pan_number => $datas) { ?>
				<?php if($datas['data_row'] == 2) { $pan_number_array[] = $pan_number; } ?>
				<tr class="main bgcolorf9f9f9 bg-font-default" id="<?php echo $datas['chksum']; ?>">
					<td rowspan="<?php echo $datas['data_row']; ?>" class="text-center">
						<?php echo $datas['channel_name']; ?><br />
						<?php echo $datas['gst_number']; ?>
					</td>
					<td rowspan="<?php echo $datas['data_row']; ?>" class="text-center"><?php echo $datas['month']; ?></td>
					<td class="text-center"><?php echo $datas['gstn']['type']; ?></td>
					<td class="text-center"><?php echo $datas['gstn']['taxable_sales']; ?></td>
					<td class="text-center"><?php echo $datas['gstn']['taxable_return']; ?></td>
					<td class="text-center"><?php echo $datas['gstn']['taxable_value']; ?></td>
					<td class="text-center"><?php echo $datas['gstn']['igst_amount']; ?></td>
					<td class="text-center"><?php echo $datas['gstn']['cgst_amount']; ?></td>
					<td class="text-center"><?php echo $datas['gstn']['sgst_amount']; ?></td>
					<td rowspan="<?php echo $datas['data_row']; ?>" class="text-right">
						<?php if($datas['flag'] == 'N'){ ?>
							<button onclick="updateGstr2x('A','<?php echo $datas['chksum']; ?>')" class="btn btn-sm btn-outline green accept"><?php echo $button_accept; ?></button>
							<button onclick="updateGstr2x('R','<?php echo $datas['chksum']; ?>')" class="btn btn-sm btn-outline red reject"><?php echo $button_reject; ?></button>
						<?php } else { ?>
							<?php echo $datas['flag_message']; ?>
						<?php } ?>
					</td>
				</tr>
				<?php if(isset($datas['upload'])){ ?>
					<tr class="main bgcolorf9f9f9 bg-font-default">
						<td class="text-center"><?php echo $datas['upload']['type']; ?></td>
						<td class="text-center <?php echo $datas['issue']['taxable_sales'] ? 'danger' : ''; ?>"><?php echo $datas['upload']['taxable_sales']; ?></td>
						<td class="text-center <?php echo $datas['issue']['taxable_return'] ? 'danger' : ''; ?>"><?php echo $datas['upload']['taxable_return']; ?></td>
						<td class="text-center <?php echo $datas['issue']['taxable_value'] ? 'danger' : ''; ?>"><?php echo $datas['upload']['taxable_value']; ?></td>
						<td class="text-center <?php echo $datas['issue']['igst_amount'] ? 'danger' : ''; ?>"><?php echo $datas['upload']['igst_amount']; ?></td>
						<td class="text-center <?php echo $datas['issue']['cgst_amount'] ? 'danger' : ''; ?>"><?php echo $datas['upload']['cgst_amount']; ?></td>
						<td class="text-center <?php echo $datas['issue']['sgst_amount'] ? 'danger' : ''; ?>"><?php echo $datas['upload']['sgst_amount']; ?></td>
					</tr>
				<?php } ?>
			<?php } ?>
		</tbody>
	</table>
</div>
<?php } ?>

<div class="row">
	<div class="fl col-lg-12 margint50">
		<div class="alert alert-info">
			<?php echo $text_gstr2x_save_manually_step; ?>
		</div>
	</div>
</div>
	
	<div class="row">
	<div class="col-lg-12 margint20">
		<div class="fl width100 note note-info">
			<span class="no-margin pull-left text-info flex"><span class="h4 no-margin font200" id="timer"></span></span>
			<h4 id="span_confirm" class="no-margin pull-right">
				<!-- <button type="button" disabled <?php echo isset($gst_credentials_check) ? 'disabled' : 'onclick="submitGSTRData()"'; ?> id="confirmBtn" class="btn btn-success mt-ladda-btn ladda-button" data-style="zoom-out"><span class="ladda-label"><?php echo $button_push_to_gst; ?></span><span class="ladda-spinner"></span><div class="ladda-progress" style="width: 0px;"></div></button> -->
				<button type="button" disabled class="btn btn-success mt-ladda-btn ladda-button tooltips" data-original-title="<?php echo $text_gstr2x_save_disabled; ?>"  data-style="zoom-out"><span class="ladda-label"><?php echo $button_push_to_gst; ?></span><span class="ladda-spinner"></span><div class="ladda-progress" style="width: 0px;"></div></button>
			</h4>
		</div>
	</div>
</div>
<script>
var accept_array = [];
var reject_array = [];
function updateGstr2x(type,chksum){ // GSTN not activated
	if(type == 'A'){
		if ($.inArray(chksum, accept_array) !== -1) {
			accept_array.splice($.inArray(chksum, accept_array),1);
			$("#gst_reports #" + chksum + " .accept").removeClass('active').addClass('no-focus').empty().append('<?php echo $button_accept; ?>');
		} else {
			accept_array.push(chksum);
			$("#gst_reports #" + chksum + " .reject").removeClass('active').addClass('no-focus').empty().append('<?php echo $button_reject; ?>')
			$("#gst_reports #" + chksum + " .accept").addClass('active').removeClass('no-focus').empty().append('<i class="fa fa-check" aria-hidden="true"></i>');
		}
		
		$("#gst_reports #" + chksum + " .reject").removeClass('active').addClass('no-focus');
		
		if ($.inArray(chksum, reject_array) !== -1) {
			reject_array.splice($.inArray(chksum, reject_array),1);
		}
	} else if(type == 'R'){
		if ($.inArray(chksum, reject_array) !== -1) {
			reject_array.splice($.inArray(chksum, reject_array),1);
			$("#gst_reports #" + chksum + " .reject").removeClass('active').addClass('no-focus').empty().append('<?php echo $button_reject; ?>')
		} else {
			reject_array.push(chksum);
			$("#gst_reports #" + chksum + " .reject").addClass('active').removeClass('no-focus').empty().append('<i class="fa fa-check" aria-hidden="true"></i>');
			$("#gst_reports #" + chksum + " .accept").removeClass('active').addClass('no-focus').empty().append('<?php echo $button_accept; ?>');
		}
		
		$("#gst_reports #" + chksum + " .accept").removeClass('active').addClass('no-focus');
		
		if ($.inArray(chksum, accept_array) !== -1) {
			accept_array.splice($.inArray(chksum, accept_array),1);
		}
	}
	
	if(accept_array.length || reject_array.length){
		$('#confirmBtn').attr('disabled',false);
	} else {
		$('#confirmBtn').attr('disabled',true);
	}
}

function confirmGSTRData(){ // Data Validation
	$.ajaxSetup({
		headers : {
			'CsrfToken': $('meta[name="csrf-token"]').attr('content')
		}
	});
	$.ajax({
		url: '<?php echo $link_gstr2x_retsave; ?>',
		data: {ret_period:ret_period, tcs_data:tcs_data, accept_array:accept_array, reject_array:reject_array},
		dataType: 'json',
		type: 'post',
		beforeSend: function () {
			$(".sa-confirm-button-container button").attr("disabled",true);
		},
		complete: function () {
			$(".sa-confirm-button-container button").attr("disabled",false);
		},
		success: function (json) {
			if (json.success) {
				if (json.message) {
					toastr['success'](json.message);
				}
			} else {
				swal("Oops", json.message, "error");
			}
		},
		timeout: <?php echo SESSION_TIMEOUT; ?>,
		error: function (xhr, ajaxOptions, thrownError) {
			toastr["error"]('<?php echo $error_ajax_error; ?>');
		}
	});
}

function submitGSTRData() {
	var tcs_data = '<?php echo json_encode($raw_tcs_data); ?>';
	var ret_period = '<?php echo isset($ret_period) ? $ret_period : ''; ?>';
	swal({
	title: "<?php echo $text_title_gstr2x_retsave; ?>",
	text: "<?php echo $text_title_gstr2x_retsave_message; ?>",
	type: "warning",
	showCancelButton: true,
	closeOnConfirm: false,
	confirmButtonText: "Yes, Submit it!",
	confirmButtonColor: "#ec6c62"
	}, function() {
		$.ajaxSetup({
			headers : {
				'CsrfToken': $('meta[name="csrf-token"]').attr('content')
			}
		});
		$.ajax(
			{
				url: '<?php echo $link_gstr2x_retsave; ?>',
				data: {ret_period:ret_period, tcs_data:tcs_data, accept_array:accept_array, reject_array:reject_array},
				dataType: 'json',
				type: 'post',
				beforeSend: function () {
					$(".sa-confirm-button-container button").attr("disabled",true);
				},
				complete: function () {
					$(".sa-confirm-button-container button").attr("disabled",false);
				},
				success: function (json) {
					if (json.success) {
						if(json.message){
							toastr['success'](json.message);
						}
					} else {
						swal("Oops", json.message, "error");
					}
				}
			}
		)
		.done(function(json) {
			if (json.success) {
				swal.close();
				<!-- SetTimerForGST(json.reference_id,json.ret_period); -->
				$('#confirmBtn').attr('disabled',true);
			} else {
				if(json.auth_require){
					var gst = $('select[name=\'filter_gst\']').val();
					generateGstOtp(gst);
				}
				toastr['error'](json.message);
			}
	})
		.error(function(json) {
			swal("Oops", json.message, "error");
		});
	});
}
</script>

<script type="text/javascript">
$(document).ready(function() {
  // First, make sure all breakup containers are hidden initially
  $('.breakup-container').hide();
  
  // Simple toggle function for breakup visibility
  $('.toggle-breakup').on('click', function(e) {
    e.preventDefault();
    e.stopPropagation();
    
    var ctin = $(this).data('ctin');
    var mainRow = $(this).closest('tr');
    var nextRow = mainRow.next('tr');
    var breakupContent = $('#breakup-' + ctin);
    
    // Check if the next row is already a breakup row for this CTIN
    if (nextRow.hasClass('breakup-row-' + ctin)) {
      // If it exists, remove it
      nextRow.remove();
      $(this).removeClass('fa-minus-circle').addClass('fa-plus-circle');
      mainRow.removeClass('expanded-row');
    } else {
      // Remove any existing breakup rows
      $('.breakup-row').remove();
      $('.toggle-breakup').removeClass('fa-minus-circle').addClass('fa-plus-circle');
      $('.main').removeClass('expanded-row');
      
      // Create a new row with the breakup content
      var colCount = mainRow.find('td').length;
      var newRow = $('<tr class="breakup-row breakup-row-' + ctin + '"><td colspan="' + colCount + '"><div class="breakup-wrapper"></div></td></tr>');
      
      // Clone the breakup content and append it to the new row
      breakupContent.clone().show().appendTo(newRow.find('.breakup-wrapper'));
      
      // Insert the new row after the main row
      mainRow.after(newRow);
      
      // Update the toggle icon
      $(this).removeClass('fa-plus-circle').addClass('fa-minus-circle');
      mainRow.addClass('expanded-row');
    }
  });
  
  // Make rows with breakup data visually distinct
  $('.main[data-ctin]').addClass('has-breakup');
});
</script>

<style>
/* Styling for the toggle functionality */
.has-breakup {
  cursor: pointer;
}

.expanded-row {
  background-color: #e8f4fc !important;
}

.toggle-breakup {
  cursor: pointer;
  color: #337ab7;
  margin-right: 5px;
}

.breakup-row td {
  padding: 0 !important;
  background-color: #f0f8ff;
  border-left: 2px solid #337ab7;
}

/* Add a wrapper div to control width */
.breakup-wrapper {
  width: 80% !important;
  margin: 0 !important; /* Changed from auto to 0 for left alignment */
  margin-left: 30px !important; /* Add left margin for spacing */
  padding: 15px 0 !important;
}

/* Ensure the breakup container takes full width of its parent */
.breakup-wrapper .breakup-container {
  display: block !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  background-color: #fff !important; /* Set background to white */
  border: 1px solid #e9f0f7 !important; /* Add border for definition */
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

.breakup-wrapper .card {
  margin-bottom: 0 !important;
  border: none !important;
  background-color: #fff !important;
}

.breakup-wrapper .card-header {
  background-color: #e8f4fc !important;
  padding: 10px 15px !important;
  color: #337ab7 !important;
  font-weight: 500 !important;
  border-bottom: 1px solid #d1e6f9 !important;
}

.breakup-wrapper .card-body {
  padding: 0 !important;
  background-color: #fff !important;
}

.breakup-wrapper .table {
  margin-bottom: 0 !important;
  background-color: #fff !important;
}

.breakup-wrapper .table th {
  background-color: #f5f9fc !important;
  color: #666 !important;
  font-weight: 500 !important;
  border-bottom: 1px solid #d1e6f9 !important;
}

.breakup-wrapper .table tbody {
  background-color: #fff !important; /* Explicitly set tbody background to white */
}

.breakup-wrapper .table td {
  border-color: #e9f0f7 !important;
  background-color: #fff !important; /* Ensure all cells have white background */
}

.breakup-wrapper .table tr.total-row {
  background-color: #f5f9fc !important;
  font-weight: bold !important;
}

.breakup-wrapper .table tr:hover {
  background-color: #f8fbff !important;
}

.breakup-container .table tbody tr td {
    padding: 8px !important; /* Use !important to ensure it takes precedence */
}

/* Add this rule to override the vertical alignment */
.table > tbody > tr > td {
    vertical-align: top !important; /* This will override the middle alignment */
}
</style>
