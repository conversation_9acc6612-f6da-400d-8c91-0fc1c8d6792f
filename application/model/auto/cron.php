<?php
defined('_PATH') or die('Restricted!');

class ModelAutoCron extends Model
{
    private $amazon_channel_id = 1;
    private $gst_rates = [0, 3, 5, 12, 18, 28];
    private $allowed_difference = 1; // Allowed difference in percentage

    public function smartUploadSelection($report_type, $report_step)
    {
        $query = $this->db->query("SELECT u.row_count,ut.*,TIMESTAMPDIFF(SECOND, ut.start_time, ut.end_time) AS time_diff
                                        FROM " . DB_PREFIX . "upload_time ut
                                        LEFT OUTER JOIN " . DB_PREFIX . "upload u ON u.upload_id = ut.upload_id AND u.report_type = '" . $this->db->escape($report_type) . "' AND u.note = '' AND u.row_count <> 0
                                        WHERE type = '" . $this->db->escape($report_type) . "'
                                            AND start_time <> '0000-00-00 00:00:00'
                                            AND end_time <> '0000-00-00 00:00:00'
                                            AND ut.step = " . $this->db->escape($report_step) . "
                                            AND u.note = ''
                                            AND u.row_count <> 0");
        $query = $query->rows;
        if (COUNT($query)) { // need to calulate maximum processable row count to avoid extra load on server
            return $query;
        } else {
            return false;
        }
    }

    public function checkStateByPincode($pincode)
    {
        $query = $this->db->query("SELECT s.name AS state_name FROM " . DB_PREFIX . "pincode_data pd
                                    JOIN " . DB_PREFIX . "state s ON s.id = pd.state_id
                                        WHERE pd.pincode = '" . $this->db->escape($pincode) . "'");
        if ($query->row) {
            return $query->row['state_name'];
        } else {
            return false;
        }
    }

    // uploadedStuckBatchReLoad START
    public function reloadStuckBatches()
    { // Update processing batch if its older that 20 min.
        $this->db->query("UPDATE " . DB_PREFIX . "upload
                            SET
                                cron = 1,
                                modified_date = NOW()
                            WHERE modified_date < NOW() + INTERVAL 20 MINUTE
                                AND cron = 4
                                AND status = 2");
    }
    // uploadedStuckBatchReLoad END

    public function getReportType($report_code)
    {
        $query = $this->db->query("SELECT dataread, createxml FROM " . DB_PREFIX . "report_type WHERE report_code = '" . $this->db->escape($report_code) . "'");
        $result = $query->row;
        if ($result) {
            return $result;
        } else {
            return false;
        }
    }

    public function getCronUpload($status, $testing_mode)
    {
        $query = $this->db->query("SELECT u.upload_id,u.gst_id,rt.report_id,u.report_type,u.reference,u.channel_id,u.edition_id,u.usr,u.charge,ud.file_name
                                    FROM " . DB_PREFIX . "upload u
                                    LEFT JOIN " . DB_PREFIX . "upload_data ud ON ud.upload_id = u.upload_id
                                    JOIN " . DB_PREFIX . "channel_edition ce ON ce.edition_id = u.edition_id AND ce.testing = " . (int)$testing_mode . " 
                                    JOIN " . DB_PREFIX . "report_type rt ON rt.report_code = u.report_type
                                    WHERE u.report_type = 'S'
                                        AND u.cron = " . (int)$status . "
                                        AND u.status NOT IN(4, 0) ");
        return $query->rows;
    }

    public function getCronUploadByReference($reference)
    {
        $query = $this->db->query("SELECT upload_id,gst_id,reference,channel_id,edition_id,usr FROM " . DB_PREFIX . "upload
                                    WHERE reference = '" . $this->db->escape($reference) . "' AND status NOT IN('4','0') ");

        if ($query->row) {
            return $query->row;
        } else {
            return false;
        }
    }

    public function updateUpload($upload_id, $status, $note)
    {
        $this->db->query("UPDATE " . DB_PREFIX . "upload
                            SET
                                cron = '" . (int)$status . "',
                                status = '" . (int)$status . "',
                                note = '" . $this->db->escape($note) . "',
                                modified_date = NOW()
                            WHERE upload_id = " . (int)$upload_id . " ");
    }

    public function getAllUploadTransactionTypes($all_upload_ids)
    {
        $upload_id_string = implode(',', $all_upload_ids);
        $query = $this->db->query("SELECT transaction_type FROM " . DB_PREFIX . "az_invoice WHERE FIND_IN_SET(upload_id, '" . $this->db->escape($upload_id_string) . "') GROUP BY transaction_type");
        $result = $query->rows;
        if (COUNT($result)) {
            $result = array_column($result, 'transaction_type');
            return $result;
        } else {
            return false;
        }
    }

    public function updateUploadBatchCronStatusBulk($upload_ids, $cron_status, $status, $note)
    {
        $upload_ids = implode(",", $upload_ids);

        if (!empty($note)) {
            $this->db->query("UPDATE " . DB_PREFIX . "upload
                                SET
                                    cron = '" . (int)$cron_status . "',
                                    status = '" . (int)$status . "',
                                    note = '" . $this->db->escape($note) . "',
                                    modified_date = NOW()
                                WHERE upload_id IN (" . $upload_ids . ") ");
        } else {
            $this->db->query("UPDATE " . DB_PREFIX . "upload
                                SET
                                    cron = '" . (int)$cron_status . "',
                                    status = '" . (int)$status . "',
                                    modified_date = NOW()
                                WHERE upload_id IN (" . $upload_ids . ") ");
        }
    }

    public function updateUploadBatchStatus($upload_id, $cron_status, $upload_batch_status, $note, $invoice_count = false)
    {
        if (!empty($note)) {
            $note_text = $note;
        } else {
            $note_text = '';
        }

        if ($invoice_count) {
            $this->db->query("UPDATE " . DB_PREFIX . "upload SET
                                count = " . (int)$invoice_count . ",
                                cron = " . (int)$cron_status . ",
                                status = " . (int)$upload_batch_status . ",
                                note = '" . $this->db->escape($note_text) . "',
                                modified_date = NOW()
                                    WHERE upload_id = " . (int)$upload_id . " ");
        } else {
            $this->db->query("UPDATE " . DB_PREFIX . "upload SET
                                cron = " . (int)$cron_status . ",
                                status = " . (int)$upload_batch_status . ",
                                note = '" . $this->db->escape($note_text) . "',
                                modified_date = NOW()
                                    WHERE upload_id = " . (int)$upload_id . " ");
        }

        if ($cron_status == 2) { // Remove upload data entry from csv stored file
            $this->db->query("DELETE FROM " . DB_PREFIX . "upload_data WHERE upload_id = " . (int)$upload_id . "");
        }
    }

    public function updateUploadRowCount($upload_id, $row_count)
    {
        $this->db->query("UPDATE " . DB_PREFIX . "upload SET
                            row_count = " . (int)$row_count . ",
                            modified_date = NOW()
                                WHERE upload_id = " . (int)$upload_id . " ");
    }

    public function getTransactionTypeByUploadId($upload_id)
    {
        $query = $this->db->query("SELECT DISTINCT(transaction_type) AS transaction_type FROM " . DB_PREFIX . "az_invoice
                                            WHERE upload_id = " . (int)$upload_id . " ");
        $result = $query->rows;
        if (COUNT($result)) {
            return array_values(array_column($result, 'transaction_type'));
        } else {
            return array();
        }
    }

    protected function getUploadData($upload_id)
    {
        $query = $this->db->query("SELECT u.*,cd.recalculate AS recalculate,cd.cancel AS cancel,cd.city_name_update FROM " . DB_PREFIX . "upload u
                                        LEFT JOIN " . DB_PREFIX . "channel_edition cd ON cd.edition_id = u.edition_id
                                            WHERE u.upload_id = " . (int)$upload_id . " ");

        if ($query->row) {
            return $query->row;
        } else {
            return false;
        }
    }

    protected function getChannelData($channel_id)
    {
        $query = $this->db->query("SELECT c.channel_id,c.name AS name,c.legal_name,c.address_1,c.address_2,c.gst_number,s.name AS state_name
                                        FROM " . DB_PREFIX . "channel c
                                        LEFT JOIN " . DB_PREFIX . "state s ON s.id = c.state_id
                                            WHERE channel_id = " . (int)$channel_id . " ");
        if ($query->row) {
            return $query->row;
        } else {
            return false;
        }
    }

    protected function getEditionData($edition_id)
    {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "channel_edition WHERE edition_id = " . (int)$edition_id . " ");
        if ($query->row) {
            return $query->row;
        } else {
            return false;
        }
    }

    protected function checkDuplicateOrder($upload_data)
    {
        $this->bug->write("START checkDuplicateOrder");
        // Old Query
        // $sql = "SELECT azi.invoice_number,azi.order_id,azi.invoice_date
        // FROM " . DB_PREFIX . "az_invoice azi
        // JOIN " . DB_PREFIX . "upload u ON u.upload_id = azi.upload_id AND u.upload_id = " . (int)$upload_data['upload_id'] . " AND u.usr = " . (int)$upload_data['usr'] . "
        // WHERE EXISTS
        // (
        // SELECT az.invoice_number
        // FROM " . DB_PREFIX . "az_invoice az
        // JOIN " . DB_PREFIX . "upload up ON up.upload_id = az.upload_id AND up.upload_id NOT IN(" . (int)$upload_data['upload_id'] . ") AND up.channel_id = " . (int)$upload_data['channel_id'] . " AND up.gst_id = " . (int)$upload_data['gst_id'] . " AND up.usr = " . (int)$upload_data['usr'] . "
        // WHERE azi.invoice_number = az.invoice_number
        // AND DATE_FORMAT(azi.invoice_date,'%Y%m%d') = DATE_FORMAT(az.invoice_date,'%Y%m%d')
        // AND azi.order_id = az.order_id
        // AND azi.shipment_id = az.shipment_id
        // AND azi.shipment_item_id = az.shipment_item_id
        // AND azi.transaction_type = az.transaction_type
        // )";

        $sql = "SELECT invoice_number,order_id,invoice_date,COUNT(*) AS count,GROUP_CONCAT(az_id) AS az_ids
                FROM(
                    SELECT azi.az_id,azi.transaction_type,azi.invoice_number,azi.order_id,DATE_FORMAT(azi.invoice_date,'%Y%m%d') AS invoice_date,azi.shipment_id,azi.shipment_item_id,azi.asin
                        FROM " . DB_PREFIX . "az_invoice azi
                        JOIN " . DB_PREFIX . "upload u ON u.upload_id = azi.upload_id AND u.upload_id = " . (int)$upload_data['upload_id'] . " AND u.usr = " . (int)$upload_data['usr'] . " AND u.gst_id = " . (int)$upload_data['gst_id'] . " AND u.channel_id = " . (int)$upload_data['channel_id'] . "
                    UNION ALL
                    SELECT azi.az_id,azi.transaction_type,azi.invoice_number,azi.order_id,DATE_FORMAT(azi.invoice_date,'%Y%m%d') AS invoice_date,azi.shipment_id,azi.shipment_item_id,azi.asin
                        FROM " . DB_PREFIX . "az_invoice azi
                        JOIN " . DB_PREFIX . "upload u ON u.upload_id = azi.upload_id AND u.upload_id <> " . (int)$upload_data['upload_id'] . " AND u.usr = " . (int)$upload_data['usr'] . " AND u.gst_id = " . (int)$upload_data['gst_id'] . " AND u.channel_id = " . (int)$upload_data['channel_id'] . "
                    ) AS data
                GROUP BY transaction_type,invoice_number,order_id,shipment_id,shipment_item_id,asin
                HAVING COUNT(*) > 1";
        // Date 2020-03-19 > invoice date removed from group
        //GROUP BY transaction_type,invoice_number,invoice_date,order_id,shipment_id,shipment_item_id,asin
        $query = $this->db->query($sql);
        if (COUNT($query->rows)) { // Must need to use az_id and upload_id combination to fetch data
            // Add upload_error
            $az_ids = implode(',', array_column($query->rows, 'az_ids'));
            $this->db->query("INSERT INTO " . DB_PREFIX . "upload_error (upload_id,order_id,invoice_number,invoice_date,error_note,added_date)
                                SELECT upload_id,order_id,invoice_number,invoice_date,'" . $this->language->get('error_csv_duplicate_data') . "' AS error_note,NOW() AS added_date
                                    FROM " . DB_PREFIX . "az_invoice 
                                    WHERE upload_id = " . (int)$upload_data['upload_id'] . "
                                        AND FIND_IN_SET(az_id,'" . $this->db->escape($az_ids) . "')");

            // Delete duplicate data from table
            $this->db->query("DELETE azi,azb,azg,azo,azs,azt,aztcs,azin
                                FROM " . DB_PREFIX . "az_invoice azi
                                JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id
                                JOIN " . DB_PREFIX . "az_giftwrap azg ON azg.az_id = azi.az_id
                                JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = azi.az_id
                                JOIN " . DB_PREFIX . "az_shipping azs ON azs.az_id = azi.az_id
                                JOIN " . DB_PREFIX . "az_taxes azt ON azt.az_id = azi.az_id
                                JOIN " . DB_PREFIX . "az_tcstax aztcs ON aztcs.az_id = azi.az_id
                                LEFT JOIN " . DB_PREFIX . "az_invoice_note azin ON azin.az_id = azi.az_id
                                WHERE azi.upload_id = " . (int)$upload_data['upload_id'] . "
                                    AND FIND_IN_SET(azi.az_id,'" . $this->db->escape($az_ids) . "')");

            // Check any data available from upload_id
            $query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "az_invoice WHERE upload_id = " . (int)$upload_data['upload_id'] . "");
            $this->bug->write("END checkDuplicateOrder");
            return $query->row['total'];
        } else { // No duplicate then process ahed
            $this->bug->write("END checkDuplicateOrder");
            return true;
        }
    }

    public function addCsvDatas($cav_data, $upload_id)
    {
        // $this->bug->write("File Data Start");
        // $this->bug->write($cav_data);
        // $this->bug->write("File Data END");

        $this->bug->write("INFO addCsvDatas Start: " . $upload_id);
        $this->load->language('auto/cron');
        $this->load->model('billing/customer');
        $this->load->model('system/other');
        $this->load->model('account/order');
        $this->load->model('account/account');

        $master_upload_id = $upload_id;
        $upload_data = $this->getUploadData($upload_id);
        // $this->bug->write("Upload Data: " . json_encode($upload_data));
        $main_gst_number = $this->getCustomerMainGstNumber($upload_data['usr']);
        $main_pan_number = strtoupper(substr($main_gst_number, 2, 10));
        $customer_info = $this->getCustomer($upload_data['usr']);
        // $this->bug->write("Customer Data: " . json_encode($customer_info));
        $seller_unique_gstins = array_values(array_unique(array_column($cav_data, 'seller_gstin'))); // GST Number found from cav_data
        // $this->bug->write("Unique GST Number: " . json_encode($seller_unique_gstins));
        $gst_numbers = $this->getCustomerGstNumbers($seller_unique_gstins, $customer_info['customer_id']); // Customers GST numbers
        // $this->bug->write("GST Numbers: " . json_encode($gst_numbers));
        $upload_ids = array();
        $upload_batch_size = $this->config->get('upload_batch_size');

        // Changes Start
        if (COUNT($gst_numbers) == 1) { // Single GST Report
            $this->bug->write("START Single Data Blank Record Create: " . $upload_id);
            // Batch have single gst number update gst id according gst number
            $first_array_cav_data = reset($cav_data);
            $seller_pan_number = substr($first_array_cav_data['seller_gstin'], 2, 10);

            if ($main_pan_number == $seller_pan_number) { // GSTN PAN Check
                $upload_data['gst_id'] = array_search($first_array_cav_data['seller_gstin'], $gst_numbers);
                $this->updateGstNumberInUpload($upload_data['gst_id'], $upload_id);

                $blank_datas = array();
                $data_count = 0;
                for ($b = 0; $b < COUNT($cav_data); $b++) {
                    $blank_datas[] = '(\'' . (int)$master_upload_id . '\')';
                    $data_count++;

                    if ($data_count == $upload_batch_size) {
                        $this->db->query("INSERT INTO " . DB_PREFIX . "az_invoice (upload_id)
                                            VALUES " . implode(",", $blank_datas) . " ");
                        $blank_datas = array();
                        $data_count = 0;
                    }
                }

                if (COUNT($blank_datas)) {
                    $this->db->query("INSERT INTO " . DB_PREFIX . "az_invoice (upload_id)
                                        VALUES " . implode(",", $blank_datas) . " ");
                    $blank_datas = array();
                    $data_count = 0;
                }

                $upload_ids[$master_upload_id] = $cav_data;
                unset($cav_data);
                $this->bug->write("END Single Data Blank Record Create: " . $upload_id);
            }
        } elseif (count($gst_numbers) <> 0) { // Multi GST Report & Other pan number gst rejects
            // Wef. 08-12-2020 Multi cluster file will process separately
            $this->bug->write("START Cluster Data shorting and create csv: " . $upload_id);

            $upload_datas = array();
            $upload_batches = array();

            // Create upload batches according gst numbers
            foreach ($gst_numbers as $gst_id => $gst_number) {
                if ($gst_number == $main_gst_number) { // Main GST Number
                    $this->updateGstNumberInUpload($gst_id, $upload_id);

                    // Update Status as Uploaded
                    $this->updateUploadRowBatchStatus($upload_id, 0, 0, 1);

                    $upload_data['gst_id'] = $gst_id;
                    $upload_batches[$gst_number] = $upload_data;
                } else { // Cluster GST Number
                    $count = 0;
                    while ($count < 2) {
                        $unique_code = strtoupper(uniqid(PREFIX_UPLOAD_BATCH));
                        if (!$this->model_account_account->checkUniqueUploadCode($unique_code)) {
                            $final_unique_code = $unique_code;
                            $count++;
                        }
                    }

                    $data = array(
                        'reference'    => $final_unique_code,
                        'gst_id'       => $gst_id,
                        'cron'         => 0,
                        'status'       => 1,
                        'note'         => '',
                        'report_type'  => $upload_data['report_type'],
                        'channel_id'   => $upload_data['channel_id'],
                        'edition_id'   => $upload_data['edition_id'],
                        'usr'          => $upload_data['usr']
                    );

                    $data['upload_id'] = $this->addUpload($data);
                    $upload_batches[$gst_number] = $data;
                    $upload_datas[$gst_number] = array();
                }
            }

            $blank_datas = array();
            $data_count = 0;
            // Define data to gst numbers
            foreach ($cav_data as $data) {
                $seller_gstin = strtoupper($data['seller_gstin']);
                $seller_pan_number = substr($seller_gstin, 2, 10);
                if ($seller_pan_number == $main_pan_number) { // Needs to create excel array
                    $upload_datas[$seller_gstin][] = $data;
                } else { // Error Data if GST id difference
                    $this->log->write("DANGER Getting Different GST");
                    $this->log->write($data);
                }
                // if ($main_gst_number == $seller_gstin) { // Main GST
                //     $blank_datas[] = '(\'' . (int)$master_upload_id . '\')';
                //     $data_count++;
                //     if ($data_count == $upload_batch_size) {
                //         $this->db->query("INSERT INTO " . DB_PREFIX . "az_invoice (upload_id)
                //                             VALUES " . implode(",", $blank_datas) . " ");
                //         $blank_datas = array();
                //         $data_count = 0;
                //     }

                //     $upload_ids[$upload_id][] = $data;
                // } elseif ($seller_pan_number == $main_pan_number) { // Needs to create excel array
                //     $upload_datas[$seller_gstin][] = $data;
                // } else { // Error Data if GST id difference
                //     $this->log->write("DANGER Getting Different GST");
                //     $this->log->write($data);
                // }
            }
            if (COUNT($blank_datas)) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "az_invoice (upload_id)
                                    VALUES " . implode(",", $blank_datas) . " ");
                $blank_datas = array();
                $data_count = 0;
            }

            // Creating Excel file for another records
            foreach ($upload_datas as $seller_gstin => $cluster_data) {
                // if ($main_gst_number <> $seller_gstin) { // Cluster GST
                $upload_batche = $upload_batches[$seller_gstin];
                $filename = $upload_batche['reference'] . CSV_FILE_EXTENSION;
                $master_array[] = array('sheet_name' => '', 'header' => array_keys(reset($cluster_data)), 'data' => $cluster_data);
                $excel_file_name = $this->excel->writeExcelArray($filename, $master_array, true);
                unset($master_array);
                unset($cluster_data);

                $this->updateClusterUploadFileName($upload_batche['upload_id'], $excel_file_name);
                // }
            }
            unset($upload_datas);

            $this->bug->write("END Cluster Data shorting and create csv: " . $upload_id);
            // Process main gst data and create another gst data's csv file

            return array();
        }
        // Changes End

        // $this->bug->write("Upload Batch Keys: " . json_encode(array_keys($upload_ids)));

        $upload_batch_response = array();
        if (COUNT($upload_ids)) {
            foreach ($upload_ids as $upload_id => $cav_data) {
                $temp_upload_data = $this->getUploadData($upload_id);
                // Customized Settings by channel to channel
                if ($upload_data['channel_id'] == $this->amazon_channel_id) { // For Amazon.in Only
                    $buyer_contact_datas = $this->updateAzCustomerInfoByUploadId($upload_data['channel_id'], array_unique(array_column($cav_data, 'order_id')), $upload_data['usr']);
                }

                $invoice_count = 0;
                $skipped_records = array();
                $row_count = 0;
                $row_count_limit = 1000;
                $error_data = array();
                $read_error = array();
                $warning_data = array();

                $az_invoice = array();
                $az_billing = array();
                $az_taxes = array();
                $az_shipping = array();
                $az_giftwrap = array();
                $az_tcstax = array();
                $az_other = array();

                $cashback_array = array();

                // Get unique ids from database
                $blank_az_ids = $this->db->query("SELECT az_id
                                                    FROM " . DB_PREFIX . "az_invoice
                                                    WHERE upload_id = " . (int)$upload_id . "");
                $az_ids = $blank_az_ids->rows;

                // check cav_data count and unique ids count
                for ($i = 0; $i < count($az_ids); $i++) {
                    // Add unique id with this.
                    $az_id = $az_ids[$i]['az_id'];
                    if (in_array($cav_data[$i]['seller_gstin'], $gst_numbers)) {
                        // Verify Buyer GST
                        $buyer_gst = isset($cav_data[$i]['buyer_gst']) ? $cav_data[$i]['buyer_gst'] : '';
                        if (in_array(strlen($buyer_gst), [0, 15])) {
                            // Verify Marketplace Id = ASIN is exisit or not
                            if ($validation_message = $this->validateData($cav_data[$i])) {
                                $error_data[] = array('order_id' => $cav_data[$i]['order_id'], 'invoice_number' => $cav_data[$i]['invoice_number'], 'invoice_date' => $cav_data[$i]['invoice_date'], 'error_note' => $validation_message, 'error_code' => 424);
                                continue;
                            }

                            // Calculate Principal Amount Basis if feature on
                            if ($temp_upload_data['recalculate']) {
                                $cav_data[$i]['total_tax_amount'] = (float)array_sum(array($cav_data[$i]['cgst_tax'], $cav_data[$i]['sgst_tax'], $cav_data[$i]['igst_tax'], $cav_data[$i]['utgst_tax']));
                                switch ($cav_data[$i]['transaction_type']) {
                                    case 7:
                                    case 10:
                                        $cav_data[$i]['principal_amount_basis'] = (float)$cav_data[$i]['principal_amount_basis'] + ((float)$cav_data[$i]['invoice_amount'] - ((float)$cav_data[$i]['principal_amount_basis'] + (float)$cav_data[$i]['shipping_amount_basis'] + (float)$cav_data[$i]['shipping_promo_discount_basis'] + (float)$cav_data[$i]['total_tax_amount'] + (float)$cav_data[$i]['item_promo_discount_basis'] + (float)$cav_data[$i]['cod_amount_basis'] + (float)$cav_data[$i]['cod_promo_discount_basis']));
                                        break;
                                    case 8:
                                        $cav_data[$i]['principal_amount_basis'] = (float)$cav_data[$i]['principal_amount_basis'] + (((float)$cav_data[$i]['invoice_amount'] + (float)$cav_data[$i]['item_promo_discount_basis']) - ((float)$cav_data[$i]['principal_amount_basis'] + (-1 * (float)$cav_data[$i]['shipping_amount_basis']) + (-1 * (float)$cav_data[$i]['shipping_promo_discount_basis']) + (-1 * (float)$cav_data[$i]['total_tax_amount']) + (-1 * (float)$cav_data[$i]['cod_amount_basis']) + (-1 * (float)$cav_data[$i]['cod_promo_discount_basis']))); // Invoice amount after all promo discount applied
                                        break;
                                }
                            }

                            if ($temp_upload_data['channel_id'] == $this->amazon_channel_id) {
                                if ($cav_data[$i]['sgst_tax'] <> 0 && $cav_data[$i]['cgst_tax'] <> 0) { // Local Sales
                                    $total_tax_amount = bcmul($cav_data[$i]['total_tax_amount'], 100);
                                    if ($total_tax_amount % 2 <> 0) {
                                        $total_tax_amount -= 1;
                                    }

                                    $cav_data[$i]['total_tax_amount'] = bcdiv($total_tax_amount, 100, 2);

                                    switch ($cav_data[$i]['transaction_type']) {
                                        case 7:
                                        case 10:
                                            $cav_data[$i]['principal_amount_basis'] = (float)$cav_data[$i]['principal_amount_basis'] + ((float)$cav_data[$i]['invoice_amount'] - ((float)$cav_data[$i]['principal_amount_basis'] + (float)$cav_data[$i]['shipping_amount_basis'] + (float)$cav_data[$i]['gift_wrap_amount_basis'] + (float)$cav_data[$i]['shipping_promo_discount_basis'] + (float)$cav_data[$i]['gift_wrap_discount_basis'] + (float)$cav_data[$i]['total_tax_amount'] + (float)$cav_data[$i]['item_promo_discount_basis'] + (float)$cav_data[$i]['cod_amount_basis'] + (float)$cav_data[$i]['cod_promo_discount_basis']));
                                            break;
                                        case 8:
                                            $cav_data[$i]['principal_amount_basis'] = (float)$cav_data[$i]['principal_amount_basis'] + (((float)$cav_data[$i]['invoice_amount'] + (float)$cav_data[$i]['item_promo_discount_basis']) - ((float)$cav_data[$i]['principal_amount_basis'] + (-1 * (float)$cav_data[$i]['shipping_amount_basis']) + (-1 * (float)$cav_data[$i]['gift_wrap_amount_basis']) + (-1 * (float)$cav_data[$i]['shipping_promo_discount_basis']) + (-1 * (float)$cav_data[$i]['gift_wrap_discount_basis']) + (-1 * (float)$cav_data[$i]['total_tax_amount']) + (-1 * (float)$cav_data[$i]['cod_amount_basis']) + (-1 * (float)$cav_data[$i]['cod_promo_discount_basis']))); // Invoice amount after all promo discount applied
                                            break;
                                    }
                                }
                            }

                            // Amazon && Cashbak Journal
                            if ($cashback_data = $this->isCashbackJournal($temp_upload_data['channel_id'], $cav_data[$i])) {
                                $cashback_array[] = $cashback_data;
                                $warning_data[] = '(\'' . (int)$upload_id . '\', \'' . $this->db->escape($cav_data[$i]['order_id']) . '\', \'' . $this->db->escape($cav_data[$i]['invoice_number']) . '\', \'' . $this->db->escape(date($this->language->get('datetime_format_sql'), strtotime($cav_data[$i]['invoice_date']))) . '\', \'' . $this->db->escape($this->language->get('error_cashback_journal_entry')) . '\',NOW())';
                                $skipped_records[] = $az_id;
                                continue;
                            }

                            // Amazon && Cancel invoice with Blank invoice number
                            if ($this->isCancelBlankInvoice($temp_upload_data['channel_id'], $cav_data[$i])) {
                                $warning_data[] = '(\'' . (int)$upload_id . '\', \'' . $this->db->escape($cav_data[$i]['order_id']) . '\', \'' . $this->db->escape($cav_data[$i]['invoice_number']) . '\', \'' . $this->db->escape(date($this->language->get('datetime_format_sql'), strtotime($cav_data[$i]['invoice_date']))) . '\', \'' . $this->db->escape($this->language->get('error_blank_invoice_number_cancel_record')) . '\',NOW())';
                                $skipped_records[] = $az_id;
                                continue;
                            }

                            // Verification
                            if ($error_message = $this->totalVerify($cav_data[$i])) {
                                $error_data[] = array('order_id' => $cav_data[$i]['order_id'], 'invoice_number' => $cav_data[$i]['invoice_number'], 'invoice_date' => $cav_data[$i]['invoice_date'], 'error_note' => $error_message, 'error_code' => 425);
                                continue;
                            }

                            $order_id = $cav_data[$i]['order_id'];

                            // Buyer GSTN Validation
                            if (strlen($buyer_gst) == 15) {
                                if (!$this->gst->validGst($buyer_gst)) {
                                    $warning_data[] = '(\'' . (int)$upload_id . '\', \'' . $this->db->escape($order_id) . '\', \'' . $this->db->escape($cav_data[$i]['invoice_number']) . '\', \'' . $this->db->escape(date($this->language->get('datetime_format_sql'), strtotime($cav_data[$i]['invoice_date']))) . '\', \'' . $this->db->escape(sprintf($this->language->get('error_buyer_gstn_invalid'), $buyer_gst)) . '\',NOW())';
                                }
                            }

                            // In Amazon Case need to change sales data invoice_date with shipment_date if its in new month
                            if ($temp_upload_data['channel_id'] == $this->amazon_channel_id) {
                                // For Amazon.in Only
                            }

                            // Update params
                            $ship_to_city = !empty(trim($cav_data[$i]['ship_to_city'])) ? htmlentities(ucwords(strtolower($cav_data[$i]['ship_to_city']))) : '';

                            // For Buyer data updation, data received only how is available in mws_order_data
                            if (isset($buyer_contact_datas[$order_id])) {
                                $buyer_datas = $buyer_contact_datas[$order_id];

                                $customer_billing_name = $buyer_datas['shipping_name'];
                                $customer_billing_address = $buyer_datas['shipping_address'];
                                $bill_to_state = htmlentities(ucwords(strtolower($cav_data[$i]['bill_to_state'])));
                                $customer_shipping_name = $buyer_datas['shipping_name'];
                                $customer_shipping_address = $buyer_datas['shipping_address'];
                                $ship_to_state = htmlentities(ucwords(strtolower($cav_data[$i]['ship_to_state'])));
                                $buyer_email = $buyer_datas['buyer_email'];
                                $buyer_phone = $buyer_datas['phone'];
                            } elseif ((strtolower($cav_data[$i]['ship_to_country']) == 'in') or (strtolower($cav_data[$i]['ship_to_country']) == 'india')) { // For India Sales
                                $customer_billing_name = !empty(TRIM($cav_data[$i]['customer_billing_name'])) ? $this->encrypt->pEncrypt(htmlentities(ucwords(strtolower($cav_data[$i]['customer_billing_name'])))) : '';
                                $customer_billing_address = !empty(TRIM($cav_data[$i]['customer_billing_address'])) ? $this->encrypt->pEncrypt(htmlentities(ucwords(strtolower((string)$cav_data[$i]['customer_billing_address'])))) : '';
                                $bill_to_state = htmlentities(ucwords(strtolower($cav_data[$i]['bill_to_state'])));
                                $customer_shipping_name = !empty(TRIM($cav_data[$i]['customer_shipping_name'])) ? $this->encrypt->pEncrypt(htmlentities(ucwords(strtolower($cav_data[$i]['customer_shipping_name'])))) : '';
                                $customer_shipping_address = (isset($cav_data[$i]['customer_shipping_address']) && !empty(TRIM($cav_data[$i]['customer_shipping_address']))) ? $this->encrypt->pEncrypt(htmlentities(ucwords(strtolower((string)$cav_data[$i]['customer_shipping_address'])))) : '';
                                $ship_to_state = htmlentities(ucwords(strtolower($cav_data[$i]['ship_to_state'])));
                                $buyer_email = !empty(TRIM($cav_data[$i]['buyer_email'])) ? $cav_data[$i]['buyer_email'] : '';
                                $buyer_phone = !empty(TRIM($cav_data[$i]['buyer_phone'])) ? $this->encrypt->pEncrypt($cav_data[$i]['buyer_phone']) : '';
                            } elseif ((strtolower($cav_data[$i]['ship_to_country']) != 'in') or (strtolower($cav_data[$i]['ship_to_country']) != 'india')) { // For Export Sales
                                $customer_billing_name = !empty(TRIM($cav_data[$i]['customer_billing_name'])) ? $this->encrypt->pEncrypt(htmlentities(ucwords(strtolower($cav_data[$i]['customer_billing_name'])))) : '';
                                $billing_address = !empty(TRIM($cav_data[$i]['customer_billing_address'])) ? (string)$cav_data[$i]['customer_billing_address'] : '';
                                $billing_address .= !empty(TRIM($cav_data[$i]['bill_to_state'])) ? ', ' . (string)$cav_data[$i]['bill_to_state'] : '';
                                $customer_billing_address = $this->encrypt->pEncrypt(htmlentities(ucwords(strtolower($billing_address))));
                                $bill_to_state = '';
                                $customer_shipping_name = !empty(TRIM($cav_data[$i]['customer_shipping_name'])) ? $this->encrypt->pEncrypt(htmlentities(ucwords(strtolower($cav_data[$i]['customer_shipping_name'])))) : '';
                                $shipping_address = !empty(TRIM($cav_data[$i]['customer_shipping_address'])) ? (string)$cav_data[$i]['customer_shipping_address'] : '';
                                $shipping_address .= !empty(TRIM($cav_data[$i]['ship_to_state'])) ? ', ' . (string)$cav_data[$i]['ship_to_state'] : '';
                                $customer_shipping_address = $this->encrypt->pEncrypt(htmlentities(ucwords(strtolower($shipping_address))));
                                $ship_to_state = '';
                                $buyer_email = !empty(TRIM($cav_data[$i]['buyer_email'])) ? $cav_data[$i]['buyer_email'] : '';
                                $buyer_phone = !empty(TRIM($cav_data[$i]['buyer_phone'])) ? $this->encrypt->pEncrypt($cav_data[$i]['buyer_phone']) : '';
                            }

                            // If bill to state is blank than add it's value from ship to states
                            if (empty(trim($bill_to_state)) || trim($bill_to_state) == '-') {
                                $bill_to_state = $ship_to_state;
                                $bill_to_postal_code = $cav_data[$i]['ship_to_postal_code'];
                                $bill_to_city = $cav_data[$i]['ship_to_city'];
                                $bill_to_country = $cav_data[$i]['ship_to_country'];
                            } else {
                                $bill_to_state = $bill_to_state;
                                $bill_to_postal_code = $cav_data[$i]['bill_to_postal_code'];
                                $bill_to_city = $cav_data[$i]['bill_to_city'];
                                $bill_to_country = $cav_data[$i]['bill_to_country'];
                            }


                            $dispatch_date = !empty(TRIM($cav_data[$i]['dispatch_date'])) ? date($this->language->get('datetime_format_short_sql'), strtotime($cav_data[$i]['dispatch_date'])) : '';
                            $product_sku = htmlentities($cav_data[$i]['sku']);
                            $product_note = !empty(TRIM($cav_data[$i]['product_note'])) ? htmlentities($cav_data[$i]['product_note']) : '';
                            $export_bill_no = !empty(TRIM($cav_data[$i]['export_bill_no'])) ? htmlentities($cav_data[$i]['export_bill_no']) : '';
                            $export_port_code = !empty(TRIM($cav_data[$i]['export_port_code'])) ? htmlentities($cav_data[$i]['export_port_code']) : '';
                            $tracking_number = $cav_data[$i]['tracking_number'] ?? '';
                            $dispatch_through = $cav_data[$i]['dispatch_through'] ?? '';

                            // GST Number define here because of getting undefined error
                            $buyer_type = !empty($buyer_gst) ? 'B2B' : 'B2C';
                            $buyer_gst_number = $buyer_gst;

                            if ($cav_data[$i]['transaction_type'] <> 9) { // Avoid cancelled record
                                if ((isset($cav_data[$i]['buyer_name']) && !empty($cav_data[$i]['buyer_name'])) && $buyer_type == 'B2C') {
                                    $read_error[] = array('order_id' => $cav_data[$i]['order_id'], 'invoice_number' => $cav_data[$i]['invoice_number'], 'invoice_date' => $cav_data[$i]['invoice_date'], 'error_note' => $this->language->get('error_buyer_gst_wrong'), 'error_code' => 421);
                                }
                            }

                            // E-invoice parameters
                            $einvoice_ack_no = $cav_data[$i]['einvoice_ack_no'] ?? '';
                            $einvoice_irn_no = $cav_data[$i]['einvoice_irn_no'] ?? '';
                            $einvoice_ack_date = isset($cav_data[$i]['einvoice_ack_date']) && !empty($cav_data[$i]['einvoice_ack_date']) ? date('Y-m-d', strtotime($cav_data[$i]['einvoice_ack_date'])) : '';

                            $az_invoice[] = '(\'' . (int)$az_id . '\', \'' . (int)$upload_id . '\', \'' . $this->db->escape($buyer_type) . '\', \'' . $this->db->escape($cav_data[$i]['invoice_number']) . '\', \'' . $this->db->escape(date($this->language->get('datetime_format_sql'), strtotime($cav_data[$i]['invoice_date']))) . '\', \'' . $this->db->escape($cav_data[$i]['currency']) . '\', \'' . (float)$cav_data[$i]['currency_conversion'] . '\', \'' . $this->db->escape($cav_data[$i]['invoice_amount']) . '\', \'' . (int)$cav_data[$i]['transaction_type'] . '\', \'' . $this->db->escape($cav_data[$i]['order_id']) . '\', \'' . $this->db->escape(date($this->language->get('datetime_format_sql'), strtotime($cav_data[$i]['order_date']))) . '\', \'' . $this->db->escape($cav_data[$i]['shipment_id']) . '\', \'' . $this->db->escape(date($this->language->get('datetime_format_sql'), strtotime($cav_data[$i]['shipment_date']))) . '\', \'' . $this->db->escape($cav_data[$i]['shipment_item_id']) . '\', \'' . (int)$cav_data[$i]['quantity'] . '\', \'' . $this->db->escape(htmlentities($cav_data[$i]['item_description'])) . '\', \'' . $this->db->escape($cav_data[$i]['asin']) . '\', \'' . $this->db->escape($product_sku) . '\', \'' . $this->db->escape($product_note) . '\', \'' . $this->db->escape($cav_data[$i]['product_tax_code']) . '\', \'' . $this->db->escape($cav_data[$i]['hsn_sac']) . '\', \'' . $this->db->escape($cav_data[$i]['seller_gstin']) . '\', \'' . $this->db->escape($buyer_gst_number) . '\', \'' . $this->db->escape($cav_data[$i]['buyer_name']) . '\')';
                            $az_billing[] = '(\'' . (int)$az_id . '\', \'' . $this->db->escape(htmlentities($cav_data[$i]['bill_from_postal_code'])) . '\', \'' . (int)$cav_data[$i]['ship_from_postal_code'] . '\', \'' . $this->db->escape($customer_billing_name) . '\', \'' . $this->db->escape($customer_shipping_name) . '\', \'' . $this->db->escape($customer_billing_address) . '\', \'' . $this->db->escape($customer_shipping_address) . '\', \'' . $this->db->escape($ship_to_city) . '\', \'' . $this->db->escape($ship_to_state) . '\', \'' . $this->db->escape($cav_data[$i]['ship_to_country']) . '\', \'' . $this->db->escape(htmlentities($cav_data[$i]['ship_to_postal_code'])) . '\', \'' . $this->db->escape(ucwords(strtolower(htmlentities($bill_to_city)))) . '\', \'' . $this->db->escape($bill_to_state) . '\',  \'' . $this->db->escape(htmlentities($bill_to_country)) . '\', \'' . $this->db->escape($bill_to_postal_code) . '\', \'' . $this->db->escape($buyer_email) . '\', \'' . $this->db->escape($buyer_phone) . '\')';
                            $az_taxes[] = '(\'' . (int)$az_id . '\', \'' . (float)$cav_data[$i]['tax_exclusive_gross'] . '\', \'' . (float)$cav_data[$i]['total_tax_amount'] . '\', \'' . (float)$cav_data[$i]['cgst_rate'] . '\', \'' . (float)$cav_data[$i]['sgst_rate'] . '\', \'' . (float)$cav_data[$i]['utgst_rate'] . '\', \'' . (float)$cav_data[$i]['igst_rate'] . '\', \'' . (float)$cav_data[$i]['cgst_tax'] . '\', \'' . (float)$cav_data[$i]['sgst_tax'] . '\', \'' . (float)$cav_data[$i]['igst_tax'] . '\', \'' . (float)$cav_data[$i]['utgst_tax'] . '\', \'' . (float)$cav_data[$i]['principal_amount'] . '\', \'' . (float)$cav_data[$i]['principal_amount_basis'] . '\', \'' . (float)$cav_data[$i]['item_promo_discount_basis'] . '\')';
                            $az_shipping[] = '(\'' . (int)$az_id . '\', \'' . (float)$cav_data[$i]['shipping_amount_basis'] . '\', \'' . (float)$cav_data[$i]['shipping_promo_discount_basis'] . '\', \'' . (float)$cav_data[$i]['cod_amount_basis'] . '\', \'' . (float)$cav_data[$i]['cod_promo_discount_basis'] . '\', \'' . $this->db->escape($dispatch_through) . '\', \'' . $this->db->escape($tracking_number) . '\', \'' . $this->db->escape($dispatch_date) . '\', \'' . $this->db->escape($cav_data[$i]['export_date']) . '\', \'' . $this->db->escape($export_bill_no) . '\', \'' . $this->db->escape($export_port_code) . '\')';
                            $az_giftwrap[] = '(\'' . (int)$az_id . '\', \'' . (float)$cav_data[$i]['gift_wrap_amount_basis'] . '\', \'' . (float)$cav_data[$i]['gift_wrap_discount_basis'] . '\')';
                            $az_tcstax[] = '(\'' . (int)$az_id . '\', \'' . (float)$cav_data[$i]['tcs_cgst_rate'] . '\', \'' . (float)$cav_data[$i]['tcs_cgst_amount'] . '\', \'' . (float)$cav_data[$i]['tcs_sgst_rate'] . '\', \'' . (float)$cav_data[$i]['tcs_sgst_amount'] . '\', \'' . (float)$cav_data[$i]['tcs_utgst_rate'] . '\', \'' . (float)$cav_data[$i]['tcs_utgst_amount'] . '\', \'' . (float)$cav_data[$i]['tcs_igst_rate'] . '\', \'' . (float)$cav_data[$i]['tcs_igst_amount'] . '\')';
                            $az_other[] = '(\'' . (int)$az_id . '\', \'' . $this->db->escape($cav_data[$i]['warehouse_id']) . '\', \'' . $this->db->escape($cav_data[$i]['fulfillment_channel']) . '\', \'' . $this->db->escape($cav_data[$i]['payment_method_code']) . '\',\'' . $this->db->escape($cav_data[$i]['credit_note_no']) . '\', \'' . ((empty($cav_data[$i]['credit_note_date']) or ($cav_data[$i]['credit_note_date'] == '1970-01-01 05:30:00')) ? '0000-00-00 00:00:00' : date($this->language->get('datetime_format_sql'), strtotime($cav_data[$i]['credit_note_date']))) . '\', NULLIF(\'' . $this->db->escape($einvoice_ack_no) . '\',\'\'), NULLIF(\'' . $this->db->escape($einvoice_irn_no) . '\',\'\'), NULLIF(\'' . $this->db->escape($einvoice_ack_date) . '\',\'\'))';

                            $invoice_count++;
                            $row_count++;

                            if ($row_count == $row_count_limit) {
                                $this->addCsvDatasInTables([
                                    'az_invoice'    => $az_invoice,
                                    'az_billing'    => $az_billing,
                                    'az_taxes'      => $az_taxes,
                                    'az_shipping'   => $az_shipping,
                                    'az_giftwrap'   => $az_giftwrap,
                                    'az_tcstax'     => $az_tcstax,
                                    'az_other'      => $az_other,
                                ]);
                                // Reset All Arrays
                                $az_invoice = array();
                                $az_billing = array();
                                $az_taxes = array();
                                $az_shipping = array();
                                $az_giftwrap = array();
                                $az_tcstax = array();
                                $az_other = array();

                                $row_count = 0;
                            }
                        } else {
                            $error_data[] = array('order_id' => $cav_data[$i]['order_id'], 'invoice_number' => $cav_data[$i]['invoice_number'], 'invoice_date' => $cav_data[$i]['invoice_date'], 'error_note' => $this->language->get('error_buyer_gst_wrong'), 'error_code' => 421);
                            break;
                        }
                    } else {
                        $this->log->write("INFO Model Auto/Cron/adddata Getting Blank Seller GSTN");
                        $error_data[] = array('order_id' => $cav_data[$i]['order_id'], 'invoice_number' => $cav_data[$i]['invoice_number'], 'invoice_date' => $cav_data[$i]['invoice_date'], 'error_note' => $this->language->get('error_gst_mismatch'), 'error_code' => 422);
                        break;
                    }
                }

                if ($row_count) { // Rest data add into database
                    $this->addCsvDatasInTables([
                        'az_invoice'    => $az_invoice,
                        'az_billing'    => $az_billing,
                        'az_taxes'      => $az_taxes,
                        'az_shipping'   => $az_shipping,
                        'az_giftwrap'   => $az_giftwrap,
                        'az_tcstax'     => $az_tcstax,
                        'az_other'      => $az_other,
                    ]);
                    // Reset All Arrays
                    $az_invoice = array();
                    $az_billing = array();
                    $az_taxes = array();
                    $az_shipping = array();
                    $az_giftwrap = array();
                    $az_tcstax = array();
                    $az_other = array();

                    $row_count = 0;
                }

                if (count($cashback_array)) {
                    // Insert into DB by creating Upload batch
                    if ($temp_upload_data['channel_id'] == $this->amazon_channel_id) {
                        $cb_edition_id = 3;
                    } else {
                        $cb_edition_id = '';
                    }
                    $uploadData = [
                        'reference' => $this->getUniqueCode(),
                        'report_type' => 'C',
                        'gst_id' => $temp_upload_data['gst_id'],
                        'channel_id' => $temp_upload_data['channel_id'],
                        'edition_id' => $cb_edition_id,
                        'cron' => 0,
                        'status' => 1,
                        'note' => '',
                        'usr' => $temp_upload_data['usr'],
                    ];

                    // Add into Data
                    $this->addOtherData(
                        $this->addUpload($uploadData),
                        $cashback_array
                    );
                }

                // Delete data from DB
                if (count($skipped_records)) {
                    $this->deleteIds($skipped_records, $upload_id);
                }

                if (count($warning_data)) {
                    $this->addUploadWarning($warning_data, $upload_id);
                }
                if (count($read_error)) {
                    $this->addUploaderror($read_error, $upload_id);
                }

                if (!$this->checkDuplicateOrder($temp_upload_data)) { // Duplicate data will be deleted and added into upload_error
                    $duplicate_data = true;
                } else { // Duplicate data deleted but still unique data available
                    $duplicate_data = false;
                }

                if (COUNT($error_data) || $duplicate_data) {
                    $this->addUploadError($error_data, $upload_id);
                    if ($duplicate_data) { // Duplicate datas
                        $upload_batch_response[] = array("upload_id" => $upload_id, "response" => array('success' => false, 'error_title' => $this->language->get('error_csv_duplicate_data'), 'error_code' => 420));
                    } else { // Other Errors
                        $upload_batch_response[] = array("upload_id" => $upload_id, "response" => array('success' => false, 'error_title' => $error_data[0]['error_note'], 'error_code' => $error_data[0]['error_code']));
                    }
                } elseif ($invoice_count == bcsub(count($cav_data), count($skipped_records))) {
                    $this->updateStateNameByUploadId($upload_id);
                    $this->updateCreditNoteByUploadId($upload_id);
                    $this->addCreditDebitNoteInSales($upload_id, $upload_data['channel_id'], $upload_data['usr']);
                    $this->updateCancellationData($upload_id, $upload_data['cancel']);
                    $this->addTcsCountingError($upload_id, $upload_data['channel_id']); // Taxable sales TCS amount getting 0, need to added into upload_error
                    $this->checkWrongGstCalculation($upload_id, $upload_data['gst_id']); // To get wrong gst calculation
                    if ($upload_data['city_name_update']) { // To Update City name from Pincode, also can be used in other channel
                        $this->pincodeDataUpdate($upload_id);
                    }
                    if ($upload_data['channel_id'] == $this->amazon_channel_id) { // For Amazon.in Only
                        $this->updateAzCancellationWarehouseDataByUploadId($upload_id);
                        $this->checkDuplicateCancelledOrders($upload_id, $upload_data); // Duplicate order with different quantity
                    }
                    if ($upload_data['channel_id'] == 4) { // For Paytm Only (City name not available in data so we extract from pincode_data table)
                        $this->updateAzCityInfoByUploadId($upload_id, $upload_data['channel_id'], $upload_data['usr']);
                    }
                    $this->updatePendingCashbackUpload($temp_upload_data);
                    $this->updateUploadRowBatchStatus($upload_id, $invoice_count, 1, 2);
                    $this->updatePendingPaymentBatches($upload_data['channel_id'], $upload_data['usr']); // Update Payment batch if pending

                    $upload_batch_response[] = array("upload_id" => $upload_id, "gst_id" => $temp_upload_data['gst_id'], "response" => array('success' => true, 'invoice_count' => $invoice_count));
                } else {
                    $upload_batch_response[] = array("upload_id" => $upload_id, "response" => array('success' => false, 'error_title' => $this->language->get('error_file_reading_issue'), 'error_code' => 424));
                }
            }
            $this->bug->write("INFO addCsvDatas END");
            return $upload_batch_response;
        } elseif (!$this->getOrderCount($upload_id)) {
            $upload_batch_response[] = array("upload_id" => $upload_id, "response" => array('success' => false, 'error_title' => $this->language->get('error_no_data_exisit'), 'error_code' => 423));
            return $upload_batch_response;
        } else {
            $upload_batch_response[] = array("upload_id" => $upload_id, "response" => array('success' => false, 'error_title' => $this->language->get('error_gst_mismatch'), 'error_code' => 422));
            return $upload_batch_response;
        }
    }
    /**
     * Validate Sales Mandatory Data
     * Some of data getting blank due to marketplace report issues.
     * this function do validation and avoid this type of entries
     * 
     * @return array
     */
    protected function validateData($data)
    {
        $error = array();

        // Marketplace Id
        if (empty(trim($data['asin']))) {
            $error[] = $this->language->get('error_insufficient_asin');
        }

        // Order Id
        if (empty(trim($data['order_id']))) {
            $error[] = $this->language->get('error_insufficient_order_id');
        }

        // Invoice amount
        if (!isset($data['invoice_amount'])) {
            $error[] = $this->language->get('error_insufficient_invoice_amount');
        }

        // Principal amount basis
        if (!isset($data['principal_amount_basis'])) {
            $error[] = $this->language->get('error_insufficient_principal_amount_basis');
        }

        // Ship to State
        if (in_array(strtolower(trim($data['ship_to_country'])), ['in', 'india'])) {
            if (!isset($data['ship_to_state']) || empty(trim($data['ship_to_state']))) {
                $error[] = $this->language->get('error_insufficient_ship_to_state');
            }
        }

        // Error found then implode together
        if ($error) {
            $messages = implode(', ', $error);
            return sprintf($this->language->get('error_insufficient_data_title'), $messages);
        }

        return false;
    }
    /**
     * Verify Cashback Journal Entry
     *
     * If cashback journal data then create cashback journal fine array and return
     *
     * @param int $channel_id
     * @param array $data Contain orders data
     * @return bool||array
     */
    protected function isCashbackJournal($channel_id, $data)
    {
        // return false;
        if ($channel_id == $this->amazon_channel_id && $data['quantity'] == 0) {
            if ($data['transaction_type'] == 8) { // Return
                $transaction_type = 12;
            } else { // Others
                $transaction_type = 13;
            }

            // Calulate GST Rate
            if ($data['shipping_igst_tax'] != 0) {
                $is_interstate = true;
            } else {
                $is_interstate = false;
            }
            $gst_amount = $data['total_tax_amount'];
            $gst_rate = $this->getGstRate($gst_amount, $data['tax_exclusive_gross']);

            $igst_rate = ($gst_rate && $is_interstate ? $gst_rate : 0);
            $igst_amount = ($gst_amount && $is_interstate ? $gst_amount : 0);
            $cgst_rate = ($gst_rate && !$is_interstate ? bcdiv($gst_rate, 2, 2) : 0);
            $cgst_amount = ($gst_amount && !$is_interstate ? bcdiv($gst_amount, 2, 2) : 0);
            $sgst_rate = ($gst_rate && !$is_interstate ? bcdiv($gst_rate, 2, 2) : 0);
            $sgst_amount = ($gst_amount && !$is_interstate ? bcdiv($gst_amount, 2, 2) : 0);

            return [
                'seller_gstin' => $data['seller_gstin'],
                'order_id' => $data['order_id'],
                'order_item_id' => $data['shipment_item_id'],
                'transaction_type' => $transaction_type,
                'transaction_type_2' => $data['transaction_type'],
                'invoice_number' => $data['invoice_number'],
                'invoice_date' => date($this->language->get('datetime_format_short_sql'), strtotime($data['invoice_date'])),
                'invoice_amount' => $data['invoice_amount'],
                'currency' => $data['currency'],
                'customer_delivery_state' => $data['ship_to_state'],
                'customer_delivery_country' => $data['ship_to_country'],

                'taxable_amount' => $data['tax_exclusive_gross'],
                'luxury_cess_rate' => 0,
                'luxury_cess_amount' => 0,
                'igst_rate' => $igst_rate,
                'igst_amount' => $igst_amount,
                'cgst_rate' => $cgst_rate,
                'cgst_amount' => $cgst_amount,
                'sgst_rate' => $sgst_rate,
                'sgst_amount' => $sgst_amount,

                'tcs_igst_rate' => $data['tcs_igst_rate'],
                'tcs_igst_amount' => $data['tcs_igst_amount'],
                'tcs_cgst_rate' => $data['tcs_cgst_rate'],
                'tcs_cgst_amount' => $data['tcs_cgst_amount'],
                'tcs_sgst_rate' => $data['tcs_sgst_rate'],
                'tcs_sgst_amount' => $data['tcs_sgst_amount'],
                'total_tcs_amount' => $data['total_tcs_deducted'],
            ];
        }

        return false;
    }

    /**
     * Check Amazon Cancel invoice have blank invoice number
     *
     * @param  mixed $channel_id
     * @param  mixed $data
     * @return bool
     */
    protected function isCancelBlankInvoice($channel_id, $data)
    {
        if (
            $channel_id == $this->amazon_channel_id && $data['transaction_type'] == 9
            && empty(trim($data['invoice_number']))
        ) {
            return true;
        }

        return false;
    }

    /**
     * Delete Skipped Records
     *
     * @param array $ids Az Index ids
     * @param int $upload_id
     * @return void
     */
    protected function deleteIds($ids, $upload_id)
    {
        $this->db->query("DELETE azi,azb,azs,azt,azg,azo,aztc
                            FROM " . DB_PREFIX . "az_invoice azi
                            LEFT OUTER JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id
                            LEFT OUTER JOIN " . DB_PREFIX . "az_shipping azs ON azs.az_id = azi.az_id
                            LEFT OUTER JOIN " . DB_PREFIX . "az_taxes azt ON azt.az_id = azi.az_id
                            LEFT OUTER JOIN " . DB_PREFIX . "az_giftwrap azg ON azg.az_id = azi.az_id
                            LEFT OUTER JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = azi.az_id
                            LEFT OUTER JOIN " . DB_PREFIX . "az_tcstax aztc ON aztc.az_id = azi.az_id
                            WHERE azi.az_id IN(" . implode(',', $ids) . ")
                                AND azi.upload_id = " . (int)$upload_id . " ");
    }
    /**
     * Verify Require Data
     *
     * @param array @data Contain orders data
     * @return bool
     */
    protected function totalVerify($data)
    {
        $error = array();

        // Transaction type is otherthan cancel and quantity is zero
        if ($data['transaction_type'] <> 9 && $data['quantity'] == 0) {
            $error[] = $this->language->get('error_incorrect_quantity');
        }
        // Invoice amount is filled and taxable item amount is zero
        if ($data['invoice_amount'] <> 0 && $data['principal_amount_basis'] == 0) {
            $error[] = $this->language->get('error_incorrect_item_value');
        }

        if (count($error)) {
            return sprintf($this->language->get('error_incorrect'), implode(', ', $error));
        }

        return false;
    }
    protected function addCsvDatasInTables($datas)
    {
        if (count($datas['az_invoice'])) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "az_invoice (az_id, upload_id, sales_type, invoice_number, invoice_date, currency, currency_conversion, invoice_amount, transaction_type, order_id, order_date, shipment_id, shipment_date, shipment_item_id, quantity, item_description, asin, sku, product_note, product_tax_code, hsn_sac, seller_gstin, buyer_gst, buyer_name)
                                VALUES " . implode(",", $datas['az_invoice']) . "
                                ON DUPLICATE KEY
                                UPDATE
                                    upload_id = VALUES(upload_id),
                                    sales_type = VALUES(sales_type),
                                    invoice_number = VALUES(invoice_number),
                                    invoice_date = VALUES(invoice_date), 
                                    currency = VALUES(currency),
                                    currency_conversion = VALUES(currency_conversion),
                                    invoice_amount = VALUES(invoice_amount),
                                    transaction_type = VALUES(transaction_type),
                                    order_id = VALUES(order_id),
                                    order_date = VALUES(order_date),
                                    shipment_id = VALUES(shipment_id),
                                    shipment_date = VALUES(shipment_date),
                                    shipment_item_id = VALUES(shipment_item_id),
                                    quantity = VALUES(quantity),
                                    item_description = VALUES(item_description),
                                    asin = VALUES(asin),
                                    sku = VALUES(sku),
                                    product_note = VALUES(product_note),
                                    product_tax_code = VALUES(product_tax_code),
                                    hsn_sac = VALUES(hsn_sac),
                                    seller_gstin = VALUES(seller_gstin),
                                    buyer_gst = VALUES(buyer_gst),
                                    buyer_name = VALUES(buyer_name)
                                ");
        }

        if (count($datas['az_billing'])) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "az_billing (az_id, bill_from_postal_code, ship_from_postal_code, customer_billing_name, customer_shipping_name, customer_billing_address, customer_shipping_address, ship_to_city, ship_to_state, ship_to_country, ship_to_postal_code, bill_to_city, bill_to_state, bill_to_country, bill_to_postal_code, buyer_email, buyer_phone)
                                VALUES " . implode(",", $datas['az_billing']) . " ");
        }

        if (count($datas['az_taxes'])) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "az_taxes (az_id, tax_exclusive_gross, total_tax_amount, cgst_rate, sgst_rate, utgst_rate, igst_rate, cgst_tax, sgst_tax, igst_tax, utgst_tax, principal_amount, principal_amount_basis, item_promo_discount_basis)
                                VALUES " . implode(",", $datas['az_taxes']) . " ");
        }

        if (count($datas['az_shipping'])) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "az_shipping (az_id, shipping_amount_basis, shipping_promo_discount_basis,cod_amount_basis,cod_promo_discount_basis,dispatch_through,tracking_number,dispatch_date, export_date, export_bill_no, export_port_code)
                                VALUES " . implode(",", $datas['az_shipping']) . " ");
        }

        if (count($datas['az_giftwrap'])) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "az_giftwrap (az_id, gift_wrap_amount_basis, gift_wrap_discount_basis)
                                VALUES " . implode(",", $datas['az_giftwrap']) . " ");
        }

        if (count($datas['az_tcstax'])) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "az_tcstax (az_id,tcs_cgst_rate,tcs_cgst_amount,tcs_sgst_rate,tcs_sgst_amount,tcs_utgst_rate,tcs_utgst_amount,tcs_igst_rate,tcs_igst_amount)
                                VALUES " . implode(",", $datas['az_tcstax']) . " ");
        }

        if (count($datas['az_other'])) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "az_other (az_id,warehouse_id,fulfillment_channel, payment_method_code,credit_note_no,credit_note_date,einvoice_ack_no,einvoice_irn_no,einvoice_ack_date)
                                VALUES " . implode(",", $datas['az_other']) . " ");
        }
    }

    public function getGst($gst_id)
    {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "gst WHERE gst_id = " . (int)$gst_id . "");
        return $query->row;
    }

    protected function getGstIdByGstNumber($gstin, $customer_id)
    {
        $query = $this->db->query("SELECT g.gst_id,g.is_main,s.name AS state_name
                                    FROM " . DB_PREFIX . "gst g
                                    LEFT OUTER JOIN " . DB_PREFIX . "state s ON s.tin_code = SUBSTR(g.gst_number,1,2)
                                        WHERE LOWER(gst_number) = '" . $this->db->escape(strtolower($gstin)) . "'
                                            AND customer_id = " . (int)$customer_id . " ");
        $query = $query->row;
        if ($query) {
            return $query;
        } else {
            return false;
        }
    }

    protected function addUpload($data)
    {
        $this->db->query("INSERT INTO " . DB_PREFIX . "upload SET
                                reference = '" . $this->db->escape($data['reference']) . "',
                                report_type = '" . $this->db->escape($data['report_type']) . "',
                                gst_id = '" . (int)$data['gst_id'] . "',
                                channel_id = '" . (int)$data['channel_id'] . "',
                                edition_id = '" . (int)$data['edition_id'] . "',
                                cron = '" . (int)$data['cron'] . "',
                                status = '" . (int)$data['status'] . "',
                                note = '" . $this->db->escape($data['note']) . "',
                                added_date = NOW(),
                                ip = '" . $this->db->escape(USER_IP) . "',
                                usr = '" . (int)$data['usr'] . "'");
        $upload_id = $this->db->getLastId();

        $comment = sprintf($this->language->get('text_activity_upload_sales'), $data['reference']);
        $this->activity->add($comment, $data['usr']);

        return $upload_id;
    }

    /**
     * Add other data to upload table
     *
     * @param int $upload_id
     * @param array $other_data
     *
     * @return void
     */
    protected function addOtherData($upload_id, $other_data)
    {
        $other_data = json_encode($other_data);
        $this->db->query("UPDATE " . DB_PREFIX . "upload
                            SET
                                other_data = '" . $this->db->escape($other_data) . "'
                            WHERE upload_id = " . (int)$upload_id . "");
    }

    public function checkRemoteIds($upload_id)
    {
        $query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "upload_remoteid WHERE upload_id = " . (int)$upload_id . " ");
        if ($query->row['total']) {
            return true;
        } else {
            return false;
        }
    }
    public function addRemoteIds($remoteid_data, $reference, $upload_id)
    {
        // Remove old remoteids
        $this->db->query("DELETE FROM " . DB_PREFIX . "upload_remoteid WHERE upload_id = " . (int)$upload_id . "");

        // Add new remoteids
        $remoteid_json = json_encode($remoteid_data);
        $string = '(\'' . (int)$upload_id . '\', \'' . $this->db->escape(strtolower($reference)) . '\', \'' . $this->db->escape($remoteid_json) . '\')';
        $this->db->query("INSERT INTO " . DB_PREFIX . "upload_remoteid (upload_id,reference,remoteids)
                            VALUES " . $string . "
                            ON DUPLICATE KEY UPDATE reference = VALUES(reference), remoteids = VALUES(remoteids)");
    }

    protected function updateClusterUploadFileName($upload_id, $file_name)
    {
        $this->db->query("INSERT INTO " . DB_PREFIX . "upload_data
                            SET
                                upload_id = " . (int)$upload_id . ",
                                file_name = '" . $this->db->escape($file_name) . "'");
    }
    protected function getCustomer($customer_id)
    {
        // State connection left join some time company tincode getting numeric entry due to this getting issue.
        $query = $this->db->query("SELECT c.*,cc.*, CONCAT(c.firstname,' ',c.lastname) AS fullname,s.name AS state_name
                                    FROM " . DB_PREFIX . "customer c
                                    JOIN " . DB_PREFIX . "customer_company cc ON cc.customer_id = c.customer_id
                                    LEFT JOIN " . DB_PREFIX . "state s ON s.tin_code = cc.state
                                        WHERE c.customer_id = '" . (int)$customer_id . "'");
        return $query->row;
    }
    protected function getCustomerMainGstNumber($customer_id)
    {
        $query = $this->db->query("SELECT gst_number FROM " . DB_PREFIX . "gst WHERE customer_id = " . (int)$customer_id . " AND is_main = 1");
        return $query->row['gst_number'];
    }
    //protected function getCustomerGstNumbers($customer_id){
    public function getCustomerGstNumbers($to_gstins, $customer_id)
    {
        $this->data = $this->load->language('auto/cron');

        $query = $this->db->query("SELECT
                                        gst_number,company_name
                                    FROM " . DB_PREFIX . "gst
                                    WHERE customer_id = " . (int)$customer_id . "
                                    ORDER BY is_main ASC");
        $gst_numbers = $query->rows;
        if (COUNT($gst_numbers)) {
            $state_data = $this->getStateData();
            $state_short_code = $this->getStateShortCodes();
            $data_array = array();
            $main_company_name = $gst_numbers[0]['company_name'];
            $main_company_name = (strpos($main_company_name, ' - ') !== false) ? strstr($main_company_name, ' - ', true) : $main_company_name;

            $gst_numbers = array_values(array_column($gst_numbers, 'gst_number'));
            $pan_number = strtolower(substr($gst_numbers[0], 2, -3));

            // New GST Number will be added into Database
            $diff_gst_numbers = array_values(array_diff($to_gstins, $gst_numbers));
            foreach ($diff_gst_numbers as $diff_gst_number) {
                if (($pan_number == strtolower(substr($diff_gst_number, 2, -3))) && !$this->checkGstNumber($diff_gst_number)) {
                    $state_code = substr($diff_gst_number, 0, 2);
                    $state_name = $state_data[$state_code];
                    $company_name = $main_company_name . ' - ' . $state_short_code[$state_code];
                    $display_name = $main_company_name;

                    // GST Unique Code Create
                    $gst_number = strtoupper($diff_gst_number);
                    $count = 0;
                    while ($count < 2) {
                        $unique_code = md5(uniqid($gst_number, true));
                        if (!$this->checkGstUniqueCode($unique_code)) {
                            $final_unique_code = $unique_code;
                            $count++;
                        }
                    }

                    if ($gst_info = $this->gst->getGstData($gst_number)) {
                        $date_of_register = $gst_info['date_of_registration'];
                        $address = $gst_info['address'];
                        $city = $gst_info['city'];
                        $pincode = $gst_info['pincode'];
                    } else {
                        $request = array('gst_number' => $gst_number);
                        $service_type = array('path' => '/public/search', 'method' => 'get');
                        $api_datas = $this->gst->mastergst($request, $service_type, 0, $customer_id);
                        if (isset($api_datas['data'])) {
                            $gst_info = $this->gst->addGstData($api_datas['data']);

                            $date_of_register = $gst_info['db_date_of_registration'];
                            $address = $gst_info['address'];
                            $city = $gst_info['city'];
                            $pincode = $gst_info['pincode'];
                        } else { // False response from GST API
                            $date_of_register = '';
                            $address = '';
                            $city = '';
                            $pincode = '';
                        }
                    }

                    $data_array[] = '(\'' . $this->db->escape($diff_gst_number) . '\', \'' . $this->db->escape($date_of_register) . '\', \'' . $this->db->escape($address) . '\', \'' . $this->db->escape($city) . '\', \'' . $this->db->escape($pincode) . '\', \'' . $this->db->escape($state_name) . '\', 0, \'' . $this->db->escape($state_name) . '\', \'' . $this->db->escape($display_name) . '\', \'' . $this->db->escape($company_name) . '\', \'' . $this->db->escape($final_unique_code) . '\', \'' . (int)$customer_id . '\', NOW(), \'' . $this->db->escape(USER_IP) . '\')';

                    $comment = sprintf($this->language->get('text_add_gst_number'), $diff_gst_number);
                    $this->notification->add($comment, $customer_id);
                }
            }

            if (COUNT($data_array)) { // New gst data added into cluster
                $this->db->query("INSERT INTO " . DB_PREFIX . "gst (gst_number,date_of_register,address,city,pincode,state, is_main, location, display_name, company_name, unique_code, customer_id, added_date, ip)
                                    VALUES " . implode(',', $data_array) . " ");
            }
        }

        // Fetch GST Number of Client as mention in funtion
        $query = $this->db->query("SELECT gst_id, gst_number FROM " . DB_PREFIX . "gst
                                    WHERE customer_id = " . (int)$customer_id . "
                                        AND FIND_IN_SET(gst_number,'" . $this->db->escape(implode(',', $to_gstins)) . "')");
        if (COUNT($query->rows)) {
            return array_column($query->rows, 'gst_number', 'gst_id');
        } else {
            return array();
        }
    }
    protected function checkGstUniqueCode($unique_code)
    {
        $query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "gst WHERE unique_code = '" . $this->db->escape($unique_code) . "' ");
        return $query->row['total'];
    }
    protected function getStateData()
    {
        $query = $this->db->query("SELECT name, tin_code FROM  " . DB_PREFIX . "state");
        if (count($query->rows)) {
            return array_column($query->rows, 'name', 'tin_code');
        } else {
            return false;
        }
    }
    protected function getStateShortCodes()
    {
        $query = $this->db->query("SELECT short_code, tin_code FROM  " . DB_PREFIX . "state");
        if (count($query->rows)) {
            return array_column($query->rows, 'short_code', 'tin_code');
        } else {
            return false;
        }
    }
    protected function checkGstNumber($gst_number)
    {
        $query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "gst WHERE gst_number = '" . (int)$this->db->escape(strtoupper($gst_number)) . "' ");
        return $query->row['total'];
    }

    public function getCustomerGstNumbersByGstId($gst_id, $customer_id)
    {
        $query = $this->db->query("SELECT UPPER(gst_number) AS gst_number FROM " . DB_PREFIX . "gst WHERE gst_id = " . (int)$gst_id . " AND customer_id = " . (int)$customer_id . " ");
        if ($query->row) {
            return $query->row['gst_number'];
        } else {
            return false;
        }
    }

    protected function updateGstNumberInUpload($gst_id, $upload_id)
    {
        $this->db->query("UPDATE " . DB_PREFIX . "upload SET gst_id = " . (int)$gst_id . " WHERE upload_id = " . (int)$upload_id . " ");
    }

    protected function updatePendingPaymentBatches($channel_id, $customer_id)
    {
        $query = $this->db->query("SELECT u.*,rt.report_id,rt.report_code
                                    FROM " . DB_PREFIX . "upload u
                                    JOIN " . DB_PREFIX . "report_type rt ON rt.report_code = u.report_type
                                    WHERE u.channel_id = " . (int)$channel_id . "
                                        AND u.status = 2
                                        AND u.report_type = 'P'
                                        AND u.usr = " . (int)$customer_id . "
                                    GROUP BY u.channel_id");
        if ($query->row) {
            $this->load->model('system/other');
            $this->model_system_other->dataHealthCheckup($query->row);
        }
    }

    // Upload Batch Processing Timing -- START
    // Step used for uploaded file steps
    public function insertUploadStartTime($upload_id, $type, $step)
    {
        $this->db->query("INSERT INTO " . DB_PREFIX . "upload_time SET
                            upload_id = " . (int)$upload_id . ",
                            type = '" . $this->db->escape($type) . "',
                            step = " . (int)$step . ",
                            start_time = NOW(),
                            start_server_load = " . (float)$this->server->getRecentServerLoad() . " ");

        return $this->db->getLastId();
    }

    public function updateUploadEndTime($time_id)
    {
        $this->db->query("UPDATE " . DB_PREFIX . "upload_time SET
                            end_time = NOW(),
                            end_server_load = " . (float)$this->server->getRecentServerLoad() . "
                                WHERE time_id = " . (int)$time_id . " ");
    }
    // Upload Batch Processing Timing -- END

    protected function updatePendingCashbackUpload($upload_data)
    {
        $query = $this->db->query("SELECT upload_id
                                    FROM " . DB_PREFIX . "upload
                                    WHERE gst_id = " . (int)$upload_data['gst_id'] . "
                                        AND channel_id = " . (int)$upload_data['channel_id'] . "
                                        AND usr = " . (int)$upload_data['usr'] . "
                                        AND cron = 3
                                        AND status = 2");
        if (count($query->rows)) {
            foreach ($query->rows as $row) {
                $upload_data = $this->getUploadData($row['upload_id']);
                if (!$this->model_account_account->checkCashbackDataHealthByReference($upload_data)) {
                    if ($health_error_note = $this->model_system_other->dataHealthCheckup($upload_data['upload_id'])) {
                        $this->model_account_account->updateUploadBatchStatus($upload_data['upload_id'], 3, 2, $health_error_note);
                    } else {
                        $this->model_account_account->updateUploadBatchStatus($upload_data['upload_id'], 2, 2, '');
                    }
                }
            }
        }
    }
    protected function updateUploadRowBatchStatus($upload_id, $invoice_count, $cron_status, $upload_batch_status)
    {
        $this->db->query("UPDATE " . DB_PREFIX . "upload SET
                            row_count = " . (int)$invoice_count . ",
                            cron = " . (int)$cron_status . ",
                            status = " . (int)$upload_batch_status . ",
                            modified_date = NOW()
                                WHERE upload_id = " . (int)$upload_id . " ");
    }

    // Data insert from above insert query and front side blank state query
    public function addUploadError($error_data, $upload_id)
    {
        $datas = array();
        foreach ($error_data as $data) {
            $datas[] = '(\'' . (int)$upload_id . '\', \'' . $this->db->escape($data['order_id']) . '\', \'' . $this->db->escape($data['invoice_number']) . '\', \'' . $this->db->escape($data['invoice_date']) . '\', \'' . $this->db->escape($data['error_note']) . '\', NOW())';
        }

        if (COUNT($datas)) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "upload_error (upload_id, order_id, invoice_number, invoice_date, error_note, added_date)
                                VALUES " . implode(',', $datas) .  " ");
        }
    }

    protected function addUploadWarning($warning_data, $upload_id)
    {
        $this->db->query("INSERT INTO " . DB_PREFIX . "upload_warning (upload_id,order_id,invoice_number,invoice_date,warning_note,added_date)
                            VALUES " . implode(',', $warning_data) . " ");
    }

    protected function getStates($postal_codes)
    {
        $query = $this->db->query("SELECT p.pincode,s.name AS state_name FROM " . DB_PREFIX . "pincode_data p
                                    LEFT OUTER JOIN " . DB_PREFIX . "state s ON s.id = p.state_id
                                        WHERE FIND_IN_SET(p.pincode,'" . $this->db->escape(implode(',', $postal_codes)) . "')");
        if (COUNT($query->rows)) {
            return $query->rows;
        } else {
            return array();
        }
    }
    public function addUnmatchedStates($unmatched_states, $upload_id)
    { // Updated 13-06-2019
        // Ship_to_state
        $query = $this->db->query("SELECT ship_to_postal_code,ship_to_state
                                    FROM " . DB_PREFIX . "az_invoice azi
                                    JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id AND FIND_IN_SET(azb.ship_to_state, '" . $this->db->escape(implode(',', $unmatched_states)) . "')
                                        WHERE azi.upload_id = " . (int)$upload_id . " ");
        if (COUNT($query->rows)) {
            $state_data = $this->getStates(array_unique(array_column($query->rows, 'ship_to_postal_code')));
            foreach ($state_data as $data) {
                $this->db->query("UPDATE " . DB_PREFIX . "az_invoice azi
                                    JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id AND azb.ship_to_postal_code = " . (int)$data['pincode'] . "
                                    SET
                                        azb.ship_to_state = '" . $this->db->escape($data['state_name']) . "'
                                    WHERE azi.upload_id = " . (int)$upload_id . " ");
            }
        }

        // Bill_to_state
        $query = $this->db->query("SELECT bill_to_postal_code,bill_to_state
                                    FROM " . DB_PREFIX . "az_invoice azi
                                    JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id AND FIND_IN_SET(azb.bill_to_state, '" . $this->db->escape(implode(',', $unmatched_states)) . "')
                                        WHERE azi.upload_id = " . (int)$upload_id . " ");
        if (COUNT($query->rows)) {
            $state_data = $this->getStates(array_unique(array_column($query->rows, 'bill_to_postal_code')));
            foreach ($state_data as $data) {
                $this->db->query("UPDATE " . DB_PREFIX . "az_invoice azi
                                    JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id AND azb.bill_to_postal_code = " . (int)$data['pincode'] . "
                                    SET
                                        azb.bill_to_state = '" . $this->db->escape($data['state_name']) . "'
                                    WHERE azi.upload_id = " . (int)$upload_id . " ");
            }
        }
        $this->updateUploadBatchStatus($upload_id, 2, 2, '');
        // New End
    }

    public function productDataAdd($upload_id, $customer_id, $channel_id)
    {
        // Group By ASIN To get unique ASIN
        $this->db->query("INSERT INTO " . DB_PREFIX . "product (sku,channel_id,asin,hsn_sac,tax_rate,item_description,customer_id,added_date,modified_date,ip)
                                SELECT sku,channel_id,asin,hsn_sac,tax_rate,item_description,customer_id,added_date,modified_date,user_ip
                                    FROM (SELECT * FROM(
                                            SELECT ai.upload_id,u.channel_id,ai.sku,ai.asin,ai.hsn_sac,ai.item_description,u.usr,NOW() AS added_date,NOW() AS modified_date,'" . $this->db->escape(USER_IP) . "' AS user_ip,usr AS customer_id,
                                                (CASE
                                                    WHEN sgst_rate <> 0 THEN sgst_rate + cgst_rate + igst_rate
                                                    WHEN utgst_rate <> 0 THEN utgst_rate + cgst_rate + igst_rate
                                                    ELSE sgst_rate + utgst_rate + cgst_rate + igst_rate
                                                END) AS tax_rate
                                                    FROM " . DB_PREFIX . "az_invoice ai
                                                        JOIN " . DB_PREFIX . "az_taxes azt ON azt.az_id = ai.az_id
                                                        JOIN " . DB_PREFIX . "upload u ON u.upload_id = ai.upload_id AND u.upload_id = " . (int)$upload_id . "
                                                            WHERE u.usr = " . (int)$customer_id . "
                                                                AND u.upload_id = " . (int)$upload_id . "
                                                                AND u.channel_id = " . (int)$channel_id . "
                                                                AND ai.upload_id = " . (int)$upload_id . "
                                                                AND ai.sku != ''
                                                                AND ai.transaction_type NOT IN(9)) AS product_data
                                            GROUP BY product_data.asin
                                        ) AS data
                                        WHERE NOT EXISTS (SELECT p.asin FROM " . DB_PREFIX . "product p
                                                            WHERE p.asin = data.asin
                                                                AND p.customer_id = data.customer_id
                                                                AND p.channel_id = data.channel_id)");

        // Update unmapped product data (hsn_sac and tax rate) from sales data
        $this->db->query("UPDATE " . DB_PREFIX . "product p
                            JOIN " . DB_PREFIX . "az_invoice azi ON azi.asin = p.asin AND azi.hsn_sac <> '' AND azi.upload_id = " . (int)$upload_id . "
                            JOIN " . DB_PREFIX . "az_taxes azt ON azt.az_id = azi.az_id
                            JOIN " . DB_PREFIX . "upload u ON u.upload_id = azi.upload_id
                            SET
                                p.hsn_sac = IF(p.hsn_sac = '',azi.hsn_sac,p.hsn_sac),
                                p.tax_rate = IF(p.tax_rate = 0,(azt.sgst_rate+azt.cgst_rate+azt.igst_rate),p.tax_rate)
                            WHERE p.customer_id = " . (int)$customer_id . "
                                AND p.channel_id = " . (int)$channel_id . "
                                AND p.hsn_sac = ''
                                AND p.tax_rate = 0
                                AND p.master_id = 0");

        // Update SKU As per mws_product_data
        $this->db->query("UPDATE " . DB_PREFIX . "product p
                            JOIN " . DB_PREFIX . "mws_product_data pd ON pd.asin = p.asin AND pd.customer_id = p.customer_id
                            JOIN " . DB_PREFIX . "remarketing_api ra ON ra.api_id = pd.api_id AND ra.channel = " . (int)$channel_id . "
                            SET
                                p.sku = pd.sku
                            WHERE p.channel_id = " . (int)$channel_id . " AND p.customer_id = " . (int)$customer_id . " AND p.master_id = 0
                                AND EXISTS(SELECT azi.asin FROM " . DB_PREFIX . "az_invoice azi WHERE azi.upload_id = " . (int)$upload_id . " AND azi.asin = p.asin GROUP BY azi.asin)");

        // Master product id auto assigned
        //$this->AIProductMapping($customer_id,$upload_id);
    }

    public function warehouseDataAdd($gst_id, $upload_id, $customer_id, $channel_id)
    {
        // Update existing warehouse if GST Id is blank
        $this->db->query("UPDATE " . DB_PREFIX . "channel_warehouse cw
                            SET
                                cw.gst_id = " . (int)$gst_id . "
                            WHERE cw.channel_id = " . (int)$channel_id . " AND cw.customer_id = " . (int)$customer_id . " AND cw.gst_id = 0
                                AND EXISTS (SELECT azo.warehouse_id FROM " . DB_PREFIX . "az_other azo
                                             JOIN " . DB_PREFIX . "az_invoice azi ON azi.az_id = azo.az_id AND azi.upload_id = " . (int)$upload_id . "
                                             WHERE azo.warehouse_id = cw.code)");

        // Adding new warehouse
        $this->db->query("INSERT INTO " . DB_PREFIX . "channel_warehouse (code,channel_id,gst_id,added_date,modified_date,ip,customer_id)
                            SELECT DISTINCT azo.warehouse_id,'" . (int)$channel_id . "',u.gst_id,NOW(),NOW(), u.ip, '" . (int)$customer_id . "'
                                FROM " . DB_PREFIX . "az_other azo
                                JOIN " . DB_PREFIX . "az_invoice azi ON azi.az_id = azo.az_id AND azi.upload_id = " . (int)$upload_id . "
                                JOIN " . DB_PREFIX . "upload u ON u.upload_id = azi.upload_id AND u.upload_id = " . (int)$upload_id . " AND u.channel_id = " . (int)$channel_id    . " AND u.usr = " . (int)$customer_id . "
                                    WHERE azo.warehouse_id NOT IN (SELECT cw.code FROM " . DB_PREFIX . "channel_warehouse cw
                                                                    WHERE cw.customer_id = u.usr
                                                                        AND cw.gst_id = u.gst_id
                                                                        AND cw.channel_id = u.channel_id)");
        //AND azi.transaction_type NOT IN(9)
    }

    protected function AIProductMapping($customer_id, $upload_id)
    {
        $unmapped_products = $this->db->query("SELECT DISTINCT ai.sku,p.product_id FROM " . DB_PREFIX . "az_invoice ai
                                    LEFT JOIN " . DB_PREFIX . "upload u ON u.upload_id = ai.upload_id
                                    LEFT JOIN " . DB_PREFIX . "product p ON p.sku = ai.sku
                                        WHERE u.usr = '" . (int)$customer_id . "'
                                            AND p.customer_id = '" . (int)$customer_id . "'
                                            AND u.upload_id = '" . (int)$upload_id . "'
                                            AND ai.sku != ''
                                            AND NOT EXISTS (SELECT p.asin FROM " . DB_PREFIX . "product p
                                                                    WHERE p.sku = ai.sku
                                                                        AND p.customer_id = " . (int)$customer_id . "
                                                                        AND p.master_id != 0)");

        $unmapped_products = $unmapped_products->rows;

        $mapped_products = $this->db->query("SELECT product_id,sku,master_id FROM " . DB_PREFIX . "product WHERE master_id != 0 AND customer_id = " . (int)$customer_id . " ");
        $mapped_products = $mapped_products->rows;

        //$products = array();
        $product_value = array();
        foreach ($unmapped_products as $unmapped_product) {
            $product_sku_one = $unmapped_product['sku'];
            if (strpos($product_sku_one, '-') !== false) {
                $product_sku_two = str_replace('-', ' ', $product_sku_one);
            } else {
                $product_sku_two = str_replace(' ', '-', $product_sku_one);
            }

            foreach ($mapped_products as $mapped_product) {
                if (($mapped_product['sku'] == $product_sku_one) || ($mapped_product['sku'] == $product_sku_two)) {
                    //$products[] = array('product_id' => $unmapped_product['product_id'], 'sku' => $unmapped_product['sku'], 'master_id' => $mapped_product['master_id']);
                    $product_value[] = '(\'' . $unmapped_product['product_id'] . '\',\'' . $unmapped_product['sku'] . '\',\'' . $mapped_product['master_id'] . '\')';
                }
            }
        }
        if (COUNT($product_value)) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "product (product_id,sku,master_id)
                                VALUES " . implode(',', $product_value) . "
                                ON DUPLICATE KEY UPDATE master_id=VALUES(master_id)");
        }

        return true;
    }

    public function deleteOrderDatasByUploadId($upload_id)
    {
        $this->db->query("DELETE i,b,ta,s,g,tc,o,su FROM " . DB_PREFIX . "az_invoice i
                            LEFT OUTER JOIN " . DB_PREFIX . "az_billing b ON b.az_id = i.az_id
                            LEFT OUTER JOIN " . DB_PREFIX . "az_taxes ta ON ta.az_id = i.az_id
                            LEFT OUTER JOIN " . DB_PREFIX . "az_shipping s ON s.az_id = i.az_id
                            LEFT OUTER JOIN " . DB_PREFIX . "az_giftwrap g ON g.az_id = i.az_id
                            LEFT OUTER JOIN " . DB_PREFIX . "az_tcstax tc ON tc.az_id = i.az_id
                            LEFT OUTER JOIN " . DB_PREFIX . "az_other o ON o.az_id = i.az_id
                            LEFT OUTER JOIN " . DB_PREFIX . "upload u ON u.upload_id = i.upload_id AND i.upload_id = " . (int)$upload_id . " AND u.status = 0
                            LEFT OUTER JOIN " . DB_PREFIX . "state_unmapped su ON su.upload_id = u.upload_id
                                WHERE i.upload_id = " . (int)$upload_id . "
                                    AND u.status = 0 ");
    }

    // Create XML
    public function checkUploadId($upload_id)
    {
        $query = $this->db->query("SELECT upload_id, reference, count, usr FROM " . DB_PREFIX . "upload
                                        WHERE upload_id = " . (int)$upload_id . "
                                            AND status = 1 ");
        if ($query->row) {
            return $query->row;
        } else {
            return false;
        }
    }

    public function getUserData($customer_id)
    {
        $query = $this->db->query("SELECT c.customer_id,c.firstname,c.lastname,c.company,c.website,c.email,c.phone,cc.address,cc.state AS state_id,cc.city,cc.gst_number,s.name AS state_name,s.id AS state,cc.charges_plan AS plan_code
                                    FROM " . DB_PREFIX . "customer c
                                        LEFT JOIN " . DB_PREFIX . "customer_company cc ON cc.customer_id = c.customer_id
                                        LEFT JOIN " . DB_PREFIX . "state s ON s.tin_code = cc.state
                                            WHERE c.customer_id = " . (int)$customer_id . " ");
        if ($query->row) {
            return $query->row;
        } else {
            return false;
        }
    }

    public function getOtherLedgers($report_id, $upload_id, $channel_id, $customer_id)
    {
        $sql = "SELECT tally_ledger AS name, mark, tax_rate
                    FROM " . DB_PREFIX . "ledger_customer lc
                    LEFT OUTER JOIN " . DB_PREFIX . "ledger l ON l.ledger_id = lc.ledger_id
                    LEFT OUTER JOIN " . DB_PREFIX . "upload u ON u.upload_id = " . (int)$upload_id . " AND lc.customer_id = u.usr AND lc.channel_id = u.channel_id
                        WHERE lc.customer_id = " . (int)$customer_id . " AND lc.channel_id = " . (int)$channel_id . " AND l.tax_module = 4 AND FIND_IN_SET(" . (int)$report_id . ",l.report_type)";
        // $this->log->write($sql);
        // $this->log->write('getOtherLedgers');
        $query = $this->db->query($sql);
        if (count($query->rows)) {
            $ledger_datas = array();
            $unique_tax_rates = array_values(array_unique(array_column($query->rows, 'tax_rate')));

            foreach ($unique_tax_rates as $tax_rate) {
                foreach ($query->rows as $q) {
                    if ($tax_rate == $q['tax_rate']) {
                        $mark = substr($q['mark'], 2, -2);
                        unset($q['mark']);
                        unset($q['tax_rate']);
                        $ledger_datas[$tax_rate][$mark] = $q['name'];
                    }
                }
            }

            return $ledger_datas;
        } else {
            return false;
        }
    }

    public function getLedgers($report_id, $customer_id, $upload_id)
    {
        $this->load->model('company/channel');
        $module_sales_gst_ledger = $this->modules->status('sales_gst_ledger', $customer_id);

        $sql = "SELECT tally_ledger AS name, mark FROM " . DB_PREFIX . "ledger_customer lc
                    LEFT JOIN " . DB_PREFIX . "ledger l ON l.ledger_id = lc.ledger_id AND FIND_IN_SET(" . (int)$report_id . ", l.report_type)
                    LEFT JOIN " . DB_PREFIX . "upload u ON u.upload_id = '" . (int)$upload_id . "'
                        WHERE lc.customer_id = u.usr
                            AND lc.channel_id = u.channel_id AND FIND_IN_SET(" . (int)$report_id . ", l.report_type) ";

        if ($module_sales_gst_ledger) {
            $sql .= " AND l.tax_module NOT IN('5','4')";
        } else {
            $sql .= " AND l.tax_module NOT IN('4')";
        }
        // $this->log->write($sql);
        // $this->log->write('getLedgers');
        $query = $this->db->query($sql);
        $query = $query->rows;
        if (COUNT($query)) {
            $datas = array();
            foreach ($query as $query) {
                $mark = substr($query['mark'], 2, -2);
                $datas[$mark] = $query['name'];
            }

            $customer_data = $this->getUserData($customer_id);
            if ($customer_data) {
                $datas['company_name'] = $customer_data['company'];
                $datas['company_city'] = $customer_data['city'];
                $datas['company_state'] = $customer_data['state_name'];
                $datas['company_state_code'] = $customer_data['state'];
            }

            $upload_data = $this->getUploadData($upload_id);
            if ($upload_data) {
                $channel_data = $this->getChannelData($upload_data['channel_id']);
                if ($channel_data) {
                    $datas['channel_name'] = $channel_data['name'];
                    $datas['channel_legal_name'] = $channel_data['legal_name'];
                    $datas['channel_address_1'] = $channel_data['address_1'];
                    $datas['channel_address_2'] = $channel_data['address_2'];
                    $datas['channel_state'] = $channel_data['state_name'];
                    $datas['channel_gst_number'] = $channel_data['gst_number'];
                }
            }
            return $datas;
        } else {
            return false;
        }
    }

    public function insertCreditAmount($charged_amount, $invoice_count, $upload_id)
    {
        $query = $this->db->query("SELECT usr AS customer_id, reference FROM " . DB_PREFIX . "upload WHERE upload_id = '" . (int)$upload_id . "' LIMIT 1");
        $customer_id = $query->row['customer_id'];
        $reference = $query->row['reference'];

        if ($charged_amount > '0') {
            $this->db->query("INSERT INTO " . DB_PREFIX . "customer_credit SET
                                customer_id = '" . (int)$customer_id . "',
                                amount = '" . $this->db->escape('-' . $charged_amount) . "',
                                description = '" . $this->db->escape('Batch Refrence: ' . $reference) . "',
                                date_added = NOW() ");
        }
        $this->updateCountUpload($invoice_count, $upload_id);

        $query = $this->db->query("SELECT SUM(amount) AS total FROM " . DB_PREFIX . "customer_credit WHERE customer_id = '" . (int)$customer_id . "' ");
        $query = round($query->row['total']);

        return $query;
    }

    public function insertCreditAmountByUploadId($charged_amount, $upload_id)
    {
        $query = $this->db->query("SELECT usr AS customer_id, reference FROM " . DB_PREFIX . "upload WHERE upload_id = " . (int)$upload_id . " LIMIT 1");
        $customer_id = $query->row['customer_id'];
        $reference = $query->row['reference'];

        $this->db->query("INSERT INTO " . DB_PREFIX . "customer_credit SET
                            customer_id = '" . (int)$customer_id . "',
                            amount = '" . $this->db->escape('-' . $charged_amount) . "',
                            description = '" . $this->db->escape('Batch Refrence: ' . $reference) . "',
                            date_added = NOW() ");
    }

    public function updateCountUpload($invoice_count, $upload_id)
    {
        $this->db->query("UPDATE " . DB_PREFIX . "upload SET
                                count = '" . $this->db->escape($invoice_count) . "' WHERE upload_id = " . (int)$upload_id . "");
    }
    /**
     * Update Credit note date if its not in current month
     */
    protected function updateCreditNoteByUploadId($upload_id)
    {
        $this->bug->write("START updateCreditNoteByUploadId");
        $this->data = $this->load->language('auto/cron');
        $warning_data = array();
        // Credit note date is older than current month
        $query = $this->db->query("SELECT DATE_FORMAT(invoice_date, '%Y-%m-01') AS invoice_date FROM " . DB_PREFIX . "az_invoice WHERE upload_id = " . (int)$upload_id . " AND transaction_type = 7 ORDER BY DATE(invoice_date) ASC LIMIT 1");
        if ($query->row) {
            $first_invoice_date = $query->row['invoice_date'];
            $query = $this->db->query("SELECT invoice_date,invoice_number,order_id FROM " . DB_PREFIX . "az_invoice
                                            WHERE upload_id = " . (int)$upload_id . "
                                                AND transaction_type = 8
                                                AND DATE(invoice_date) < DATE('" . $this->db->escape($first_invoice_date) . "') ");
            if (count($query->rows)) {
                foreach ($query->rows as $note) {
                    $warning_note = sprintf($this->language->get('warning_credit_note'), date($this->language->get('date_format_short'), strtotime($note['invoice_date'])), date($this->language->get('date_format_short'), strtotime($first_invoice_date)));
                    $warning_data[] = '(\'' . (int)$upload_id . '\', \'' . $this->db->escape($note['order_id']) . '\', \'' . $this->db->escape($note['invoice_number']) . '\', \'' . $this->db->escape($note['invoice_date']) . '\', \'' . $this->db->escape($warning_note) . '\',NOW())';
                }

                $this->db->query("UPDATE " . DB_PREFIX . "az_invoice
                                    SET
                                        invoice_date = '" . $this->db->escape($first_invoice_date) . "'
                                    WHERE upload_id = " . (int)$upload_id . "
                                        AND transaction_type = 8
                                        AND DATE(invoice_date) < DATE('" . $this->db->escape($first_invoice_date) . "') ");
            }
        }

        // Credit note date is greate than current month
        $query = $this->db->query("SELECT LAST_DAY(DATE_FORMAT(invoice_date, '%Y-%m-01')) AS invoice_date FROM " . DB_PREFIX . "az_invoice WHERE upload_id = " . (int)$upload_id . " AND transaction_type = 7 ORDER BY DATE(invoice_date) DESC LIMIT 1");
        if ($query->row) {
            $last_invoice_date = $query->row['invoice_date'];
            $query = $this->db->query("SELECT invoice_date,invoice_number,order_id
                                            FROM " . DB_PREFIX . "az_invoice
                                            WHERE upload_id = " . (int)$upload_id . "
                                                AND transaction_type = 8
                                                AND DATE(invoice_date) > DATE('" . $this->db->escape($last_invoice_date) . "')");
            if (count($query->rows)) {
                foreach ($query->rows as $note) {
                    $warning_note = sprintf($this->language->get('warning_credit_note'), date($this->language->get('date_format_short'), strtotime($note['invoice_date'])), date($this->language->get('date_format_short'), strtotime($last_invoice_date)));
                    $warning_data[] = '(\'' . (int)$upload_id . '\', \'' . $this->db->escape($note['order_id']) . '\', \'' . $this->db->escape($note['invoice_number']) . '\', \'' . $this->db->escape($note['invoice_date']) . '\', \'' . $this->db->escape($warning_note) . '\',NOW())';
                }

                $this->db->query("UPDATE " . DB_PREFIX . "az_invoice
                                    SET
                                        invoice_date = '" . $this->db->escape($last_invoice_date) . "'
                                    WHERE upload_id = " . (int)$upload_id . "
                                        AND transaction_type = 8
                                        AND DATE(invoice_date) > DATE('" . $this->db->escape($last_invoice_date) . "') ");
            }
        }

        if (COUNT($warning_data)) {
            $this->addUploadWarning($warning_data, $upload_id);
        }
        $this->bug->write("END updateCreditNoteByUploadId");
    }

    protected function getVoucherNumbers($upload_id)
    {
        $query = $this->db->query("SELECT DISTINCT(transaction_type) AS transaction_type FROM " . DB_PREFIX . "az_invoice WHERE upload_id = " . (int)$upload_id . " ");

        $query = $query->rows;
        if ($query) {
            $status = array();
            foreach ($query as $q) {
                $status[] = $q['transaction_type'];
            }
            return $status;
        } else {
            return false;
        }
    }

    protected function getUploadGstId($upload_id)
    {
        $query = $this->db->query("SELECT gst_id FROM " . DB_PREFIX . "upload WHERE upload_id = " . (int)$upload_id . " ");
        $query = $query->row;
        return $query['gst_id'];
    }

    protected function getSimilarUpload($upload_id, $channel_id, $gst_id, $customer_id)
    {
        $query = $this->db->query("SELECT upload_id
                                    FROM " . DB_PREFIX . "upload
                                    WHERE gst_id = " . (int)$gst_id . "
                                        AND channel_id = " . (int)$channel_id . "
                                        AND usr = " . (int)$customer_id . "
                                        AND report_type = 'S'
                                        AND upload_id NOT IN(" . (int)$upload_id . ")
                                        AND status IN(2,3) ");
        $result = $query->rows;

        if (COUNT($result)) {
            $upload_ids = array_column($result, 'upload_id');
            return $upload_ids;
        } else {
            return false;
        }
    }

    protected function addCreditDebitNoteInSales($upload_id, $channel_id, $customer_id)
    { // Make Faster
        $this->bug->write("START addCreditDebitNoteInSales");
        // ORDER BY invoice_date ASC => need to set bacause of getting multiple records so mysql only select last response of duplicate data, last response is accurate response
        if ($transaction_type = $this->getVoucherNumbers($upload_id)) {
            $gst_id = $this->getUploadGstId($upload_id);

            if (in_array(8, $transaction_type)) { // Credit Note
                // Update data using same upload batch
                $query = $this->db->query("UPDATE " . DB_PREFIX . "az_invoice azi
                                            JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = azi.az_id AND (azo.credit_note_no = '' OR azo.sales_az_id = 0),
                                                (SELECT az_id,invoice_number,invoice_date,order_id
                                                    FROM " . DB_PREFIX . "az_invoice
                                                    WHERE upload_id = " . (int)$upload_id . "
                                                        AND transaction_type IN(7)
                                                    ORDER BY invoice_date ASC
                                                ) az_data
                                            SET azo.sales_az_id = az_data.az_id,
                                                azo.credit_note_no = IF(azo.credit_note_no = '', az_data.invoice_number, azo.credit_note_no),
                                                azo.credit_note_date = IF(azo.credit_note_date = '0000-00-00 00:00:00', az_data.invoice_date, azo.credit_note_date)
                                            WHERE azi.upload_id = " . (int)$upload_id . "
                                                AND azi.transaction_type = 8
                                                AND azi.order_id = az_data.order_id
                                                AND azi.invoice_date >= az_data.invoice_date");

                if ($upload_ids = $this->getSimilarUpload($upload_id, $channel_id, $gst_id, $customer_id)) {
                    $query = $this->db->query("SELECT azi.order_id
                                                FROM " . DB_PREFIX . "az_invoice azi
                                                JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = azi.az_id AND (azo.credit_note_no = '' OR azo.sales_az_id = 0)
                                                WHERE azi.upload_id = " . (int)$upload_id . "
                                                    AND azi.transaction_type = 8
                                                GROUP BY azi.order_id");
                    if (COUNT($query->rows)) { // Check in old uploads
                        $query = $this->db->query("UPDATE " . DB_PREFIX . "az_invoice azi
                                                    JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = azi.az_id AND (azo.credit_note_no = '' OR azo.sales_az_id = 0),
                                                    (SELECT * FROM
                                                        (
                                                            SELECT az_id,invoice_number,invoice_date,order_id
                                                            FROM " . DB_PREFIX . "az_invoice
                                                            WHERE transaction_type IN(7)
                                                                AND upload_id IN(" . implode(',', $upload_ids) . ")
                                                                AND FIND_IN_SET(order_id, '" . implode(',', array_column($query->rows, 'order_id')) . "')
                                                            ORDER BY invoice_date ASC
                                                        ) AS data
                                                        GROUP BY order_id
                                                    ) az_data
                                                    SET azo.sales_az_id = az_data.az_id,
                                                        azo.credit_note_no = IF(azo.credit_note_no = '', az_data.invoice_number, azo.credit_note_no),
                                                        azo.credit_note_date = IF(azo.credit_note_date = '0000-00-00 00:00:00', az_data.invoice_date, azo.credit_note_date)
                                                    WHERE azi.upload_id = " . (int)$upload_id . "
                                                        AND azi.transaction_type = 8
                                                        AND azi.order_id = az_data.order_id
                                                        AND azi.invoice_date >= az_data.invoice_date");
                    }
                }
            }

            if (in_array(10, $transaction_type)) { // Debit Note
                $query = $this->db->query("UPDATE " . DB_PREFIX . "az_invoice azi
                                            JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = azi.az_id AND (azo.credit_note_no = '' OR azo.sales_az_id = 0),
                                            (
                                                SELECT az_id,invoice_number,invoice_date,order_id
                                                FROM " . DB_PREFIX . "az_invoice
                                                WHERE upload_id = " . (int)$upload_id . "
                                                    AND transaction_type IN(7)
                                                ORDER BY invoice_date ASC
                                            ) az_data
                                            SET azo.sales_az_id = az_data.az_id,
                                                azo.credit_note_no = IF(azo.credit_note_no = '', az_data.invoice_number, azo.credit_note_no),
                                                azo.credit_note_date = IF(azo.credit_note_date = '0000-00-00 00:00:00', az_data.invoice_date, azo.credit_note_date)
                                            WHERE azi.upload_id = " . (int)$upload_id . "
                                                AND azi.transaction_type = 10
                                                AND azi.order_id = az_data.order_id
                                                AND azi.invoice_date >= az_data.invoice_date");

                if ($upload_ids = $this->getSimilarUpload($upload_id, $channel_id, $gst_id, $customer_id)) {
                    $query = $this->db->query("SELECT azi.order_id
                                                FROM " . DB_PREFIX . "az_invoice azi
                                                JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = azi.az_id AND (azo.credit_note_no = '' OR azo.sales_az_id = 0)
                                                WHERE azi.upload_id = " . (int)$upload_id . "
                                                    AND azi.transaction_type IN(10)
                                                GROUP BY azi.order_id");
                    if (COUNT($query->rows)) {
                        $query = $this->db->query("UPDATE " . DB_PREFIX . "az_invoice azi
                                                    JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = azi.az_id AND (azo.credit_note_no = '' OR azo.sales_az_id = 0),
                                                    (SELECT * FROM
                                                        (
                                                            SELECT az_id,invoice_number,invoice_date,order_id
                                                                FROM " . DB_PREFIX . "az_invoice
                                                                WHERE transaction_type IN(7)
                                                                    AND FIND_IN_SET(upload_id, '" . implode(',', $upload_ids) . "')
                                                                    AND FIND_IN_SET(order_id, '" . implode(',', array_column($query->rows, 'order_id')) . "')
                                                                ORDER BY invoice_date ASC
                                                            ) AS data
                                                        GROUP BY order_id
                                                    ) az_data
                                                    SET azo.sales_az_id = az_data.az_id,
                                                        azo.credit_note_no = IF(azo.credit_note_no = '', az_data.invoice_number, azo.credit_note_no),
                                                        azo.credit_note_date = IF(azo.credit_note_date = '0000-00-00 00:00:00', az_data.invoice_date, azo.credit_note_date)
                                                    WHERE azi.upload_id = " . (int)$upload_id . "
                                                        AND azi.transaction_type = 10
                                                        AND azi.order_id = az_data.order_id
                                                        AND azi.invoice_date >= az_data.invoice_date");
                    }
                }
            }
        }

        $this->updateUploadBatchStatus($upload_id, 1, 2, '');
        $this->bug->write("END addCreditDebitNoteInSales");
    }

    public function getCountries()
    { // Data used to validate sql country code and return name-> used getInvoiceForTallyXml()
        $query = $this->db->query("SELECT UPPER(code) AS code,name FROM " . DB_PREFIX . "country");
        $query = $query->rows;

        if (count($query)) {
            $datas = array();
            foreach ($query as $q) {
                $datas[$q['code']] = $q['name'];
            }
            return $datas;
        } else {
            return array();
        }
    }
    protected function getStateDatas($min_invoice_date)
    {
        $query = $this->db->query("SELECT LOWER(name) AS name, short_code
                                    FROM " . DB_PREFIX . "state
                                    WHERE status = 1");
        $result = $query->rows;

        // Applicable Till
        $query = $this->db->query("SELECT LOWER(old_name) AS name, short_code
                                    FROM " . DB_PREFIX . "state
                                    WHERE status = 1
                                        AND applicable_till IS NOT NULL
                                        AND applicable_till >= '" . $this->db->escape($min_invoice_date) . "'");
        if ($query->rows) {
            $result = array_merge($result, $query->rows);
        }

        return array_column($result, 'short_code', 'name');
    }

    public function getInvoiceForTallyXml($transaction_type, $upload_id, $customer_id, $user_state, $country_datas)
    {
        $this->load->language('auto/cron');

        // Need to make query fast be decreasing unwanted left outer joins
        // Sol-1: if some table have limited datas like status, channel, channel_warehouse this tables put seprate and fetch its data by in_array/array_search in any foreach array
        $sql = "SELECT
                az.az_id,az.upload_id,az.sales_type,az.invoice_number,az.invoice_date,az.invoice_amount,az.transaction_type,az.order_id,az.order_date,az.shipment_item_id,az.quantity,az.item_description,az.sku,az.product_note,az.hsn_sac,az.buyer_gst,az.buyer_name,
                azb.customer_shipping_name,azb.customer_shipping_address,azb.ship_to_city,azb.ship_to_state,azb.ship_to_country,azb.ship_to_postal_code,azb.customer_billing_name,azb.customer_billing_address,azb.bill_to_city,azb.bill_to_state,azb.bill_to_country,azb.bill_to_postal_code,azb.buyer_email,azb.buyer_phone,
                azs.shipping_amount_basis,azs.shipping_promo_discount_basis,azs.cod_amount_basis,azs.cod_promo_discount_basis,azs.dispatch_through,azs.tracking_number,azs.dispatch_date,azs.export_date,azs.export_bill_no,azs.export_port_code,
                azt.tax_exclusive_gross,azt.total_tax_amount,azt.cgst_tax,(azt.sgst_tax + azt.utgst_tax) AS sgst_tax,azt.igst_tax,azt.principal_amount_basis,azt.item_promo_discount_basis,cgst_rate,(sgst_rate + utgst_rate) AS sgst_rate,utgst_rate,igst_rate,
                azg.gift_wrap_amount_basis,azg.gift_wrap_discount_basis,
                azo.warehouse_id,azo.fulfillment_channel,azo.payment_method_code,azo.credit_note_no,azo.credit_note_date,azo.einvoice_ack_no,azo.einvoice_irn_no,azo.einvoice_ack_date,
                u.usr AS usr,u.reference, pm.tally_name AS product_tally_name, pm.hsn_sac as product_hsn_sac, s.name AS transaction_status,c.name AS channel_name, cw.tally_name AS location_name, cw.address as warehouse_address, cw.city as warehouse_city, cw.pincode as warehouse_pincode
                    FROM " . DB_PREFIX . "az_invoice az
                    JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = az.az_id
                    JOIN " . DB_PREFIX . "status s ON s.status_id = az.transaction_type
                    JOIN " . DB_PREFIX . "az_shipping azs ON azs.az_id = az.az_id
                    JOIN " . DB_PREFIX . "az_taxes azt ON azt.az_id = az.az_id
                    JOIN " . DB_PREFIX . "az_giftwrap azg ON azg.az_id = az.az_id
                    JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = az.az_id
                    JOIN " . DB_PREFIX . "upload u ON u.upload_id = az.upload_id AND u.upload_id = " . (int)$upload_id . "
                    JOIN " . DB_PREFIX . "channel c ON c.channel_id = u.channel_id
                    JOIN " . DB_PREFIX . "product p ON p.asin = az.asin AND p.channel_id = u.channel_id AND p.customer_id = u.usr
                    JOIN " . DB_PREFIX . "product_master pm ON pm.master_id = p.master_id AND p.customer_id = u.usr
                    JOIN " . DB_PREFIX . "channel_warehouse cw ON cw.code = azo.warehouse_id AND u.gst_id = cw.gst_id AND cw.customer_id = u.usr AND cw.channel_id = u.channel_id
                        WHERE az.transaction_type = " . (int)$transaction_type . "
                            AND az.upload_id = " . (int)$upload_id . "
                        ORDER BY az.invoice_date,az.invoice_number ASC";
        // $this->log->write($sql);
        $query = $this->db->query($sql);
        $query_datas = $query->rows;
        $final_datas = array();
        $result_datas = array();
        $error_data = array();

        if (COUNT($query_datas)) {
            foreach ($query_datas as $query_data) {
                $temp_array_key = $query_data['transaction_type'] . $query_data['order_id'] . $query_data['invoice_number'];
                $result_datas[$temp_array_key][] = $query_data;
            }
            unset($query_datas);
        }

        $module_sales_gst_ledger = $this->modules->status('sales_gst_ledger', $customer_id); // GST Rate wise ledger for Sales Account, IGST, CGST, SGST

        if (COUNT($result_datas)) {
            // Tally Version
            $tally_version = $this->customersetting->get('general_default_tally_version', $customer_id);

            $ship_from_query = $this->db->query("SELECT 
                    s.name as state_name, g.gst_number
                FROM " . DB_PREFIX . "state s
                JOIN " . DB_PREFIX . "upload u ON u.upload_id = " . (int)$upload_id . "
                JOIN " . DB_PREFIX . "gst g ON g.gst_id = u.gst_id AND g.customer_id = u.usr
                WHERE s.tin_code = LEFT(g.gst_number,2)");
            $ship_from_state = $ship_from_query->row['state_name'];
            $seller_gstin = $ship_from_query->row['gst_number'];

            foreach ($result_datas as $query) {
                $inventories = array();

                $remove_key = array("az_id", "upload_id", "invoice_number", "invoice_date", "transaction_type", "order_id", "order_date", "buyer_gst", "buyer_name", "customer_shipping_name", "customer_shipping_address", "ship_to_city", "ship_to_state", "ship_to_country", "ship_to_postal_code", "customer_billing_name", "customer_billing_address", "bill_to_city", "bill_to_state", "bill_to_country", "bill_to_postal_code", "warehouse_id", "fulfillment_channel", "payment_method_code", "usr", "reference", "transaction_status", "channel_name", "sales_type", "shipping_amount_basis", "shipping_promo_discount_basis", "cod_amount_basis", "cod_promo_discount_basis", "item_promo_discount_basis", "gift_wrap_amount_basis", "gift_wrap_discount_basis", "credit_note_date", "credit_note_no", "cgst_tax", "sgst_tax", "utgst_tax", "igst_tax", "buyer_email", "buyer_phone", "dispatch_through", "tracking_number", "dispatch_date", "export_date", "export_bill_no", "export_port_code");

                $tax_summary = array();
                $total_invoice_amount = 0;
                $total_tax = 0;
                $total_igst_amount = 0;
                $total_gst_amount = 0;
                $total_item_promo_discount_basis = 0;
                $total_gift_wrap_amount_basis = 0;
                $total_gift_wrap_discount_basis = 0;
                $total_shipping_amount_basis = 0;
                $total_shipping_promo_discount_basis = 0;
                $total_cod_amount_basis = 0;
                $total_cod_promo_discount_basis = 0;

                $first_array = reset($query);

                if ($transaction_type == 8) {
                    if ($first_array['sales_type'] == 'B2C') {
                        $narration = sprintf($this->language->get('text_narration_title_return_retail'), $query[0]['order_id']);
                    } elseif ($first_array['sales_type'] == 'B2B') {
                        $narration = sprintf($this->language->get('text_narration_title_return_business'), $query[0]['order_id']);
                    }
                } else {
                    if ($first_array['sales_type'] == 'B2C') {
                        $narration = sprintf($this->language->get('text_narration_title_sales_retail'), $query[0]['order_id']);
                    } elseif ($first_array['sales_type'] == 'B2B') {
                        $narration = sprintf($this->language->get('text_narration_title_sales_business'), $query[0]['order_id']);
                    }
                }

                foreach ($query as $key => $q) {
                    $total_invoice_amount += $q['invoice_amount'];

                    $total_tax += $q['total_tax_amount'];
                    $total_item_promo_discount_basis += $q['item_promo_discount_basis'];
                    $total_gift_wrap_amount_basis += $q['gift_wrap_amount_basis'];
                    $total_gift_wrap_discount_basis += $q['gift_wrap_discount_basis'];
                    $total_shipping_amount_basis += $q['shipping_amount_basis'];
                    $total_shipping_promo_discount_basis += $q['shipping_promo_discount_basis'];
                    $total_cod_amount_basis += $q['cod_amount_basis'];
                    $total_cod_promo_discount_basis += $q['cod_promo_discount_basis'];

                    if ($q['igst_tax'] <> 0) {
                        $total_igst_amount += $q['total_tax_amount'];
                    }
                    if ($q['sgst_tax'] <> 0 || $q['cgst_tax'] <> 0) {
                        $total_gst_amount += $q['total_tax_amount'];
                    }

                    $inventories[$key] = array_diff_key($q, array_flip($remove_key));

                    if ($q['cgst_rate'] != 0) { // Unsed only for local sales within State
                        $total_tax_rate = array_sum(array($q['cgst_rate'], $q['sgst_rate'])); // Getting 0% Tax Rate in GSTR-1 for that igst rate added in tax rate

                        switch ($tally_version) {
                            case 'primev3':
                            case 'primev4':
                            case 'primev5':
                            case 'primev6':    
                                $inventories[$key]['tax_rate'] = sprintf($this->language->get('text_gst_tax_rate_igst_v3'), $total_tax_rate) . sprintf($this->language->get('text_gst_tax_rate_cgst_v3'), $q['cgst_rate']) . sprintf($this->language->get('text_gst_tax_rate_sgst_v3'), bcadd($q['utgst_rate'], $q['sgst_rate'], 2)) . $this->language->get('text_gst_tax_rate_cess_v3') . $this->language->get('text_gst_tax_rate_state_cess_v3');

                                $inventories[$key]['taxable_sales'] = 'Local Sales - Taxable';
                                $inventories[$key]['gst_taxability_type'] = 'Taxable';
                                break;
                            default:
                                $inventories[$key]['tax_rate'] = sprintf($this->language->get('text_gst_tax_rate_igst'), $total_tax_rate) . sprintf($this->language->get('text_gst_tax_rate_cgst'), $q['cgst_rate']) . ($q['utgst_rate'] <> 0 ? sprintf($this->language->get('text_gst_tax_rate_utgst'), $q['utgst_rate']) : sprintf($this->language->get('text_gst_tax_rate_sgst'), $q['sgst_rate']));

                                $inventories[$key]['taxable_sales'] = 'Sales Taxable';
                                $inventories[$key]['gst_taxability_type'] = 'Taxable';
                                break;
                        }
                    } elseif ($q['igst_rate'] != 0) {
                        switch ($tally_version) {
                            case 'primev3':
                            case 'primev4':
                            case 'primev5':
                            case 'primev6':    
                                $inventories[$key]['tax_rate'] = sprintf($this->language->get('text_gst_tax_rate_igst_v3'), $q['igst_rate']) . $this->language->get('text_gst_tax_rate_sgst_blank_v3') . $this->language->get('text_gst_tax_rate_cgst_blank_v3') . $this->language->get('text_gst_tax_rate_cess_v3') . $this->language->get('text_gst_tax_rate_state_cess_v3');

                                // Export Sales | Sales within India
                                $inventories[$key]['taxable_sales'] = ($first_array['ship_to_country'] != 'IN') ? 'Exports - Taxable' : 'Interstate Sales - Taxable';
                                $inventories[$key]['gst_taxability_type'] = 'Taxable';
                                break;
                            default:
                                $inventories[$key]['tax_rate'] = sprintf($this->language->get('text_gst_tax_rate_igst'), $q['igst_rate']) . $this->language->get('text_gst_tax_rate_sgst_blank') . $this->language->get('text_gst_tax_rate_cgst_blank');

                                // Export Sales | Sales within India
                                $inventories[$key]['taxable_sales'] = ($first_array['ship_to_country'] != 'IN') ? 'Exports Taxable' : 'Interstate Sales Taxable';
                                $inventories[$key]['gst_taxability_type'] = 'Taxable';
                                break;
                        }
                    } else {
                        // Common Blank Tax Rate for Nill Rated Sales
                        switch ($tally_version) {
                            case 'primev3':
                            case 'primev4':
                            case 'primev5':
                            case 'primev6':    
                                $inventories[$key]['tax_rate'] = $this->language->get('text_gst_tax_rate_igst_blank_v3') . $this->language->get('text_gst_tax_rate_cgst_blank_v3') . $this->language->get('text_gst_tax_rate_sgst_blank_v3') . $this->language->get('text_gst_tax_rate_cess_v3') . $this->language->get('text_gst_tax_rate_state_cess_v3');

                                if ($first_array['ship_to_country'] != 'IN') { // Export Sales nil rated
                                    $inventories[$key]['taxable_sales'] = 'Exports - Exempt';
                                    $inventories[$key]['gst_taxability_type'] = 'Exempt';
                                } elseif ($user_state == strtolower($first_array['ship_to_state'])) { // Local sales nil rated
                                    $inventories[$key]['taxable_sales'] = 'Local Sales - Nil Rated';
                                    $inventories[$key]['gst_taxability_type'] = 'Nil Rated';
                                } else { // Interstate sales nil rated
                                    $inventories[$key]['taxable_sales'] = 'Interstate Sales - Nil Rated';
                                    $inventories[$key]['gst_taxability_type'] = 'Nil Rated';
                                }
                                break;
                            default:
                                $inventories[$key]['tax_rate'] = $this->language->get('text_gst_tax_rate_igst_blank') . $this->language->get('text_gst_tax_rate_cgst_blank') . $this->language->get('text_gst_tax_rate_sgst_blank');

                                if ($first_array['ship_to_country'] != 'IN') { // Export Sales nil rated
                                    $inventories[$key]['taxable_sales'] = 'Exports Nil Rated';
                                    $inventories[$key]['gst_taxability_type'] = 'Exempt';
                                } elseif ($user_state == strtolower($first_array['ship_to_state'])) { // Local sales nil rated
                                    $inventories[$key]['taxable_sales'] = 'Sales Nil Rated';
                                    $inventories[$key]['gst_taxability_type'] = 'Nil Rated';
                                } else { // Interstate sales nil rated
                                    $inventories[$key]['taxable_sales'] = 'Interstate Sales Nil Rated';
                                    $inventories[$key]['gst_taxability_type'] = 'Nil Rated';
                                }
                                break;
                        }
                    }

                    // Product Narration
                    //$inventories[$key]['product_note'] = $first_array['ship_to_country'] != 'IN' ? $inventories[$key]['product_note'] : $inventories[$key]['sku'];

                     // First determine the product note based on country
                     $product_note_value = $first_array['ship_to_country'] <> 'IN' ? $inventories[$key]['product_note'] : $inventories[$key]['sku'];

                     // Apply thorough cleaning to remove any problematic sequences
                     $inventories[$key]['product_note'] = $this->thoroughlyCleanString($product_note_value);

                     //$this->log->write("Line no 2164: " . $inventories[$key]['product_note']);

                     // Final safety check - explicitly remove any remaining &nbs
                     if (strpos($inventories[$key]['product_note'], '&nbs') !== false) {
                         $inventories[$key]['product_note'] = str_replace('&nbs', '', $inventories[$key]['product_note']);
                         //$this->log->write("Product note cleaning: &nbs found and removed from product note for key: " . $inventories[$key]['product_note']);
                     }

                    if (strpos($inventories[$key]['product_tally_name'], '&amp;') !== false) {
                        $inventories[$key]['product_tally_name'] = str_replace('&amp;', '&', $inventories[$key]['product_tally_name']);
                    }
                    if (strpos($inventories[$key]['product_tally_name'], '&quot;') !== false) {
                        $inventories[$key]['product_tally_name'] = str_replace('&quot;', '"', $inventories[$key]['product_tally_name']);
                    }
                    $inventories[$key]['product_tally_name'] = htmlentities($inventories[$key]['product_tally_name']); // htmlentities used because if user upload tally product name in special charachter

                    // Add tax rate of cess and cess on qty only in debit note
                    // if ($first_array['transaction_type'] == 10) {
                    //     $inventories[$key]['tax_rate'] .= $this->language->get('text_gst_tax_rate_cess') . $this->language->get('text_gst_tax_rate_cess_on_qty');
                    // }

                    if ($q['quantity'] == 1) {
                        $inventories[$key]['product_rate_per_piece'] = $q['principal_amount_basis'];
                    } else {
                        $product_other_charges = ($q['shipping_amount_basis'] + $q['shipping_promo_discount_basis']) + ($q['cod_amount_basis'] + $q['cod_promo_discount_basis']) + ($q['gift_wrap_amount_basis'] + $q['gift_wrap_discount_basis']) + $q['item_promo_discount_basis'];
                        $product_rate_per_piece = bcdiv(bcsub($q['tax_exclusive_gross'], $product_other_charges, 2), $q['quantity'], 3);
                        if ($product_rate_per_piece < 0) {
                            $inventories[$key]['product_rate_per_piece'] = -1 * $product_rate_per_piece; // Conver Negative amount to positive
                        } else {
                            $inventories[$key]['product_rate_per_piece'] = $product_rate_per_piece;
                        }
                    }
                    $narration .= sprintf($this->language->get('text_narration_data'), htmlentities($q['item_description']), $q['quantity']);

                    if ($module_sales_gst_ledger) {
                        $sgst_rate = $inventories[$key]['sgst_rate'];
                        $cgst_rate = $inventories[$key]['cgst_rate'];
                        $igst_rate = $inventories[$key]['igst_rate'];
                        // $utgst_rate = $inventories[$key]['utgst_rate'];
                        // if Sales And GST Tax Rate Module on then run else blank array
                        if ($cgst_rate != '0.00') {
                            if (!isset($tax_summary['cgst'][$cgst_rate])) {
                                $tax_summary['cgst'][$cgst_rate] = 0;
                            }
                            $tax_summary['cgst'][$cgst_rate] += bcdiv($inventories[$key]['total_tax_amount'], 2, 4);
                        }
                        if ($sgst_rate != '0.00') {
                            if (!isset($tax_summary['sgst'][$sgst_rate])) {
                                $tax_summary['sgst'][$sgst_rate] = 0;
                            }
                            $tax_summary['sgst'][$sgst_rate] += bcdiv($inventories[$key]['total_tax_amount'], 2, 4);
                        }
                        // if($utgst_rate != '0.00'){
                        // 	if(!isset($tax_summary['utgst'][$utgst_rate])){$tax_summary['utgst'][$utgst_rate]=0;}
                        // 	$tax_summary['utgst'][$utgst_rate]+=bcdiv($inventories[$key]['total_tax_amount'],2,4);
                        // }
                        if ($igst_rate != '0.00') {
                            if (!isset($tax_summary['igst'][$igst_rate])) {
                                $tax_summary['igst'][$igst_rate] = 0;
                            }
                            $tax_summary['igst'][$igst_rate] += $inventories[$key]['total_tax_amount'];
                        }
                    }
                }

                if ($first_array['sales_type'] == 'B2C') {
                    $sales_type = 'Consumer';
                } elseif ($first_array['sales_type'] == 'B2B') {
                    $sales_type = 'Regular';
                }

                if ($first_array['transaction_type'] == 8) { // Credit Note
                    $nature_of_return = '01-Sales Return';

                    switch ($tally_version) {
                        case 'primev3':
                        case 'primev4':
                        case 'primev5':
                        case 'primev6':    
                            if (abs($total_invoice_amount) < 100000) {
                                $original_sale_value = 'Less Than or Equal to 1 Lakhs'; //KM #potter 20250429
                                $ord_sale_value = 'B2C (Small)'; //KM #potter 20250429
                            } else {
                                $original_sale_value = 'More Than 1 Lakhs'; //KM #potter 20250429
                                $ord_sale_value = 'B2C (Large)'; //KM #potter 20250429
                            }
                            break;
                        default:
                            if (abs($total_invoice_amount) < 250000) {
                                $original_sale_value = 'Lesser than or equal to 2.5 lakhs';
                                $ord_sale_value = 'B2C (Small)'; //KM #potter 20250429
                            } else {
                                $original_sale_value = 'More than or equal to 2.5 lakhs';
                                $ord_sale_value = 'B2C (Large)'; //KM #potter 20250429
                            }
                            break;
                    }

                    if (!empty($first_array['credit_note_no'])) {
                        $bill_type = 'Agst Ref';
                    } else {
                        $first_array['credit_note_no'] = $first_array['invoice_number'];
                        $bill_type = 'New Ref';
                    }
                } elseif ($first_array['transaction_type'] == 10) { // Debit Note
                    if (!empty($first_array['credit_note_no'])) {
                        $bill_type = 'Agst Ref';
                    } else {
                        $first_array['credit_note_no'] = $first_array['invoice_number'];
                        $bill_type = 'New Ref';
                    }
                } else { // Debit Note entry need check and pass for this
                    $first_array['credit_note_no'] = $first_array['invoice_number'];
                    $bill_type = 'New Ref';
                }

                if ($first_array['cgst_rate'] != 0) {
                    switch ($tally_version) {
                        case 'primev3':
                        case 'primev4':
                        case 'primev5':
                        case 'primev6':    
                            $tax_rate = $this->language->get('text_gst_tax_rate_igst_blank_v3') . "\n" . sprintf($this->language->get('text_gst_tax_rate_cgst_v3'), $first_array['cgst_rate']) . "\n" . sprintf($this->language->get('text_gst_tax_rate_sgst_v3'), bcadd($first_array['utgst_rate'], $first_array['sgst_rate'], 2)) . $this->language->get('text_gst_tax_rate_cess_v3') . $this->language->get('text_gst_tax_rate_state_cess_v3');
                            break;
                        default:
                            $tax_rate = $this->language->get('text_gst_tax_rate_igst_blank') . "\n" . sprintf($this->language->get('text_gst_tax_rate_cgst'), $first_array['cgst_rate']) . "\n" . ($first_array['utgst_rate'] <> 0 ? sprintf($this->language->get('text_gst_tax_rate_utgst'), $first_array['utgst_rate']) : sprintf($this->language->get('text_gst_tax_rate_sgst'), $first_array['sgst_rate']));
                            break;
                    }

                    $taxable_sales = 'Sales Taxable';
                } elseif ($first_array['igst_rate'] != 0) {
                    switch ($tally_version) {
                        case 'primev3':
                        case 'primev4':
                        case 'primev5':
                        case 'primev6':    
                            $tax_rate = sprintf($this->language->get('text_gst_tax_rate_igst_v3'), $first_array['igst_rate']) . "\n" . $this->language->get('text_gst_tax_rate_cgst_blank_v3') . "\n" . $this->language->get('text_gst_tax_rate_sgst_blank_v3') . $this->language->get('text_gst_tax_rate_cess_v3') . $this->language->get('text_gst_tax_rate_state_cess_v3');
                            break;
                        default:
                            $tax_rate = sprintf($this->language->get('text_gst_tax_rate_igst'), $first_array['igst_rate']) . "\n" . $this->language->get('text_gst_tax_rate_cgst_blank') . "\n" . $this->language->get('text_gst_tax_rate_sgst_blank');
                            break;
                    }

                    $taxable_sales = ($first_array['ship_to_country'] != 'IN') ? 'Exports Taxable' : 'Interstate Sales Taxable';
                } else {
                    switch ($tally_version) {
                        case 'primev3':
                        case 'primev4':
                        case 'primev5':
                        case 'primev6':    
                            $tax_rate = $this->language->get('text_gst_tax_rate_igst_blank_v3') . "\n" . $this->language->get('text_gst_tax_rate_cgst_blank_v3') . "\n" . $this->language->get('text_gst_tax_rate_sgst_blank_v3') . $this->language->get('text_gst_tax_rate_cess_v3') . $this->language->get('text_gst_tax_rate_state_cess_v3');
                            break;
                        default:
                            $tax_rate = $this->language->get('text_gst_tax_rate_igst_blank') . "\n" . $this->language->get('text_gst_tax_rate_cgst_blank') . "\n" . $this->language->get('text_gst_tax_rate_sgst_blank');
                            break;
                    }

                    if ($first_array['ship_to_country'] != 'IN') { // Export Sales nil rated
                        $taxable_sales = 'Exports Nil Rated';
                    } elseif ($user_state == strtolower($first_array['ship_to_state'])) { // Local sales nil rated
                        $taxable_sales = 'Sales Nil Rated';
                    } else { // Interstate sales nil rated
                        $taxable_sales = 'Interstate Sales Nil Rated';
                    }
                }

                $payment_methods = $this->getPaymentMethod();

                foreach ($payment_methods as $method) {
                    if (strtolower($method['search']) == strtolower($first_array['payment_method_code'])) {
                        $method_code = $method['name'];
                        break;
                    }
                }

                if (isset($method_code)) {
                    $payment_method_code = $method_code;
                } else {
                    $payment_method_code = $first_array['payment_method_code'];
                }

                //////// End Here Customer address break in part, add its city and pincode details And buyer phone and email in single variable
                // Customer Shipping Address
                $customer_shipping_address = !empty(TRIM($first_array['customer_shipping_address'])) ? $this->encrypt->pDecrypt($first_array['customer_shipping_address']) : '';
                $customer_address_basic = $customer_shipping_address;
                if (strlen($customer_shipping_address) > 55) {
                    $shipping_address_string = wordwrap($customer_shipping_address, 55, "~~", false);
                    $shipping_address_string = explode('~~', $shipping_address_string);

                    $customer_shipping_address = '';
                    $customer_address_basic = '';
                    foreach ($shipping_address_string as $address_string) {
                        $address_string = $this->limitedHtmlEntitiesConvert($address_string);
                        // if (($transaction_type == 7) or ($transaction_type == 10)) {
                        $customer_shipping_address .= '<ADDRESS>' . $address_string . '</ADDRESS>';
                        $customer_address_basic .= '<BASICBUYERADDRESS>' . $address_string . '</BASICBUYERADDRESS>';
                        // } elseif ($transaction_type == 8) {
                        //     $customer_shipping_address .= '<BASICBUYERADDRESS>' . $address_string . '</BASICBUYERADDRESS>';
                        //     $customer_address_basic .= '<BASICBUYERADDRESS>' . $address_string . '</BASICBUYERADDRESS>';
                        // }
                    }
                } elseif (!empty($customer_shipping_address)) { // Avoid blank address entry
                    $customer_shipping_address = $this->limitedHtmlEntitiesConvert($customer_shipping_address);
                    // if (($transaction_type == 7) or ($transaction_type == 10)) {
                    $customer_shipping_address = '<ADDRESS>' . $customer_shipping_address . '</ADDRESS>';
                    $customer_address_basic = '<BASICBUYERADDRESS>' . $customer_shipping_address . '</BASICBUYERADDRESS>';
                    // } elseif ($transaction_type == 8) {
                    //     $customer_shipping_address = '<BASICBUYERADDRESS>' . $customer_shipping_address . '</BASICBUYERADDRESS>';
                    //     $customer_address_basic = '<BASICBUYERADDRESS>' . $customer_shipping_address . '</BASICBUYERADDRESS>';
                    // }
                } else {
                    $customer_shipping_address = '';
                    $customer_address_basic = '';
                }

                $ship_to_city = !empty($first_array['ship_to_city']) ? $this->limitedHtmlEntitiesConvert($first_array['ship_to_city']) . ', ' : '';
                if (!empty($ship_to_city) or !empty($first_array['ship_to_postal_code'])) {
                    $ship_to_postal_code = !empty($first_array['ship_to_postal_code']) ? sprintf($this->language->get('text_tally_format_address_pincode'), $this->limitedHtmlEntitiesConvert($first_array['ship_to_postal_code'])) : '';
                    // if (($transaction_type == 7) or ($transaction_type == 10)) {
                    $customer_shipping_address .= '<ADDRESS>' . $ship_to_city . $ship_to_postal_code . '</ADDRESS>';
                    $customer_address_basic .= '<BASICBUYERADDRESS>' . $ship_to_city . $ship_to_postal_code . '</BASICBUYERADDRESS>';
                    // } elseif ($transaction_type == 8) {
                    //     $customer_shipping_address .= '<BASICBUYERADDRESS>' . $ship_to_city . $ship_to_postal_code . '</BASICBUYERADDRESS>';
                    //     $customer_address_basic .= '<BASICBUYERADDRESS>' . $ship_to_city . $ship_to_postal_code . '</BASICBUYERADDRESS>';
                    // }
                }

                if ($first_array['ship_to_country'] != 'IN') {
                    $ship_to_state = !empty($first_array['ship_to_state']) ? $this->limitedHtmlEntitiesConvert($first_array['ship_to_state']) . ', ' : '';
                    if (!empty($ship_to_state)) {
                        // if (($transaction_type == 7) or ($transaction_type == 10)) {
                        $customer_shipping_address .= '<ADDRESS>' . sprintf($this->language->get('text_tally_format_address_state'), $ship_to_state, $country_datas[$first_array['ship_to_country']]) . '</ADDRESS>';
                        $customer_address_basic .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_state'), $ship_to_state, $country_datas[$first_array['ship_to_country']]) . '</BASICBUYERADDRESS>';
                        // } elseif ($transaction_type == 8) {
                        //     $customer_shipping_address .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_state'), $ship_to_state, $country_datas[$first_array['ship_to_country']]) . '</BASICBUYERADDRESS>';
                        //     $customer_address_basic .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_state'), $ship_to_state, $country_datas[$first_array['ship_to_country']]) . '</BASICBUYERADDRESS>';
                        // }
                    }
                }

                // Customer Billing Address
                $customer_billing_address = !empty(TRIM($first_array['customer_billing_address'])) ? $this->encrypt->pDecrypt($first_array['customer_billing_address']) : '';
                if (strlen($customer_billing_address) > 55) {
                    $billing_address_string = wordwrap($customer_billing_address, 55, "~~", false);
                    $billing_address_string = explode('~~', $billing_address_string);

                    $customer_billing_address = '';
                    foreach ($billing_address_string as $address_string) {
                        $address_string = $this->limitedHtmlEntitiesConvert($address_string);
                        // if (($transaction_type == 7) or ($transaction_type == 10)) {
                        $customer_billing_address .= '<ADDRESS>' . $address_string . '</ADDRESS>';
                        // } elseif ($transaction_type == 8) {
                        //     $customer_billing_address .= '<BASICBUYERADDRESS>' . $address_string . '</BASICBUYERADDRESS>';
                        // }
                    }
                } elseif (!empty($first_array['customer_billing_address'])) {
                    $customer_billing_address = $this->limitedHtmlEntitiesConvert($customer_billing_address);
                    // if (($transaction_type == 7) or ($transaction_type == 10)) {
                    $customer_billing_address = '<ADDRESS>' . $customer_billing_address . '</ADDRESS>';
                    // } elseif ($transaction_type == 8) {
                    //     $customer_billing_address = '<BASICBUYERADDRESS>' . $customer_billing_address . '</BASICBUYERADDRESS>';
                    // }
                } else {
                    $customer_billing_address = '';
                }

                $bill_to_city = !empty($first_array['bill_to_city']) ? $this->limitedHtmlEntitiesConvert($first_array['bill_to_city']) . ', ' : '';
                if (!empty($bill_to_city) or !empty($first_array['bill_to_postal_code'])) {
                    $bill_to_postal_code = !empty($first_array['bill_to_postal_code']) ? sprintf($this->language->get('text_tally_format_address_pincode'), $this->limitedHtmlEntitiesConvert($first_array['bill_to_postal_code'])) : '';
                    // if (($transaction_type == 7) or ($transaction_type == 10)) {
                    $customer_billing_address .= '<ADDRESS>' . $bill_to_city . $bill_to_postal_code . '</ADDRESS>';
                    // } elseif ($transaction_type == 8) {
                    //     $customer_billing_address .= '<BASICBUYERADDRESS>' . $bill_to_city . $bill_to_postal_code . '</BASICBUYERADDRESS>';
                    // }
                }

                if ($first_array['ship_to_country'] != 'IN') { // Shipping address is billing address in GST LAW
                    $bill_to_state = !empty($first_array['bill_to_state']) ? $this->limitedHtmlEntitiesConvert($first_array['bill_to_state']) . ', ' : '';
                    if (!empty($bill_to_state)) {
                        // if (($transaction_type == 7) or ($transaction_type == 10)) {
                        $customer_billing_address .= '<ADDRESS>' . sprintf($this->language->get('text_tally_format_address_state_only'), $bill_to_state) . '</ADDRESS>';
                        // } elseif ($transaction_type == 8) {
                        //     $customer_billing_address .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_state_only'), $bill_to_state) . '</BASICBUYERADDRESS>';
                        // }
                    }
                }

                // Buyer Phone Number
                if (!empty(trim($first_array['buyer_phone']))) {
                    $buyer_phone = $this->encrypt->pDecrypt($first_array['buyer_phone']);
                    // if (($transaction_type == 7) or ($transaction_type == 10)) { // Sales, Debit Note
                    $customer_shipping_address .= '<ADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_phone'), $buyer_phone) . '</ADDRESS>';
                    $customer_billing_address .= '<ADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_phone'), $buyer_phone) . '</ADDRESS>';
                    $customer_address_basic .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_phone'), $buyer_phone) . '</BASICBUYERADDRESS>';
                    // } elseif ($transaction_type == 8) { // Credit Note
                    //     $customer_shipping_address .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_phone'), $buyer_phone) . '</BASICBUYERADDRESS>';
                    //     $customer_billing_address .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_phone'), $buyer_phone) . '</BASICBUYERADDRESS>';
                    //     $customer_address_basic .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_phone'), $buyer_phone) . '</BASICBUYERADDRESS>';
                    // } elseif ($transaction_type == 9) { // Sales Cancel
                    $buyer_phone = '';
                    // }
                } else {
                    $buyer_phone = '';
                }

                // Buyer Email Address
                if (!empty(trim($first_array['buyer_email']))) {
                    // if (($transaction_type == 7) or ($transaction_type == 10)) { // Sales, Debit Note
                    $customer_shipping_address .= '<ADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_email'), $first_array['buyer_email']) . '</ADDRESS>';
                    $customer_billing_address .= '<ADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_email'), $first_array['buyer_email']) . '</ADDRESS>';
                    $customer_address_basic .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_email'), $first_array['buyer_email']) . '</BASICBUYERADDRESS>';
                    // } elseif ($transaction_type == 8) { // Credit Note
                    //     $customer_shipping_address .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_email'), $first_array['buyer_email']) . '</BASICBUYERADDRESS>';
                    //     $customer_billing_address .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_email'), $first_array['buyer_email']) . '</BASICBUYERADDRESS>';
                    //     $customer_address_basic .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_email'), $first_array['buyer_email']) . '</BASICBUYERADDRESS>';
                    // } elseif ($transaction_type == 9) { // Sales Cancel
                    $buyer_email = '';
                    // }
                } else {
                    $buyer_email = '';
                }
                //////// End Here

                $ship_to_country = strtoupper($first_array['ship_to_country']);
                $bill_to_country = strtoupper($first_array['bill_to_country']);
                $is_export_sales = $ship_to_country != 'IN' ? true : false;

                switch ($transaction_type) { // Sales, Debit Note Negative Invoice Total
                    case 7:
                    case 10:
                        $total_invoice_amount = bcmul($total_invoice_amount, -1, 2);
                        break;
                }

                if (
                    !is_null($first_array['einvoice_ack_date']) && $first_array['einvoice_ack_date'] != '0000-00-00'
                    && !empty($first_array['einvoice_ack_date'])
                ) {
                    $einvoice_ack_date = date('Ymd', strtotime($first_array['einvoice_ack_date']));
                } else {
                    $einvoice_ack_date = '';
                }

                // Note: bill_to_city, bill_to_postal_code, (buyer_email), (buyer_phone) -> not require now in below array
                $final_datas[] = array(
                    'az_id'                        => $first_array['az_id'],
                    'upload_id'                    => $first_array['upload_id'],
                    'seller_gstin'              => $seller_gstin,
                    'sales_type'                => $sales_type,
                    'sales_type_code'            => $first_array['sales_type'],
                    'invoice_number'            => $first_array['invoice_number'],
                    'invoice_date'                => date($this->language->get('datetime_format_tally'), strtotime($first_array['invoice_date'])),
                    'credit_note_no'            => $first_array['credit_note_no'] ? $first_array['credit_note_no'] : '',
                    'credit_note_date'            => $first_array['credit_note_date'] != '0000-00-00 00:00:00' ? date($this->language->get('datetime_format_tally'), strtotime($first_array['credit_note_date'])) : '',
                    'order_id'                    => $first_array['order_id'],
                    'order_date'                => date($this->language->get('datetime_format_tally'), strtotime($first_array['order_date'])),
                    'buyer_gst'                    => $first_array['buyer_gst'],
                    'buyer_name'                => htmlentities($first_array['buyer_name']),
                    'bill_type'                    => $bill_type,
                    'tax_rate'                    => isset($tax_rate) ? $tax_rate : '',
                    'taxable_sales'                => $taxable_sales,
                    'original_sale_value'        => isset($original_sale_value) ? $original_sale_value : '',
                    'ord_sale_value'        => isset($ord_sale_value) ? $ord_sale_value : '',  //KM #potter 20250429
                    'nature_of_return'            => isset($nature_of_return) ? $nature_of_return : '',
                    'customer_shipping_name'    => $this->limitedHtmlEntitiesConvert($this->encrypt->pDecrypt($first_array['customer_shipping_name'])),
                    'customer_shipping_address'    => $customer_shipping_address,
                    'customer_address_basic'    => $customer_address_basic,
                    'ship_from_address'         => $first_array['warehouse_address'],
                    'ship_from_city'            => $first_array['warehouse_city'],
                    'ship_from_postal_code'     => $first_array['warehouse_pincode'],
                    'ship_from_state'            => $ship_from_state,
                    'ship_to_city'                => $this->limitedHtmlEntitiesConvert($first_array['ship_to_city']),
                    'ship_to_state'                => $is_export_sales ? '' : ucwords(strtolower($first_array['ship_to_state'])),
                    'ship_to_postal_code'        => $first_array['ship_to_postal_code'],
                    'ship_to_country'            => isset($country_datas[$ship_to_country]) ? $country_datas[$ship_to_country] : '',
                    'dispatch_through'            => htmlentities($first_array['dispatch_through']),
                    'tracking_number'            => htmlentities($first_array['tracking_number']),
                    'dispatch_date'                => (empty($first_array['dispatch_date']) or ($first_array['dispatch_date'] == '1970-01-01') or ($first_array['dispatch_date'] == '0000-00-00')) ? '' : date($this->language->get('datetime_format_tally'), strtotime($first_array['dispatch_date'])),
                    'export_date'                => (empty($first_array['export_date']) or ($first_array['export_date'] == '1970-01-01') or ($first_array['export_date'] == '0000-00-00')) ? '' : date($this->language->get('datetime_format_tally'), strtotime($first_array['export_date'])),
                    'export_bill_no'            => $first_array['export_bill_no'],
                    'export_port_code'            => $first_array['export_port_code'],
                    'customer_billing_name'        => $this->limitedHtmlEntitiesConvert($this->encrypt->pDecrypt($first_array['customer_billing_name'])),
                    'customer_billing_address'    => $customer_billing_address,
                    'bill_to_city'                => $this->limitedHtmlEntitiesConvert($first_array['bill_to_city']),
                    'bill_to_state'                => $is_export_sales ? '' : $first_array['bill_to_state'],
                    'bill_to_postal_code'        => $first_array['bill_to_postal_code'],
                    'bill_to_country'            => isset($country_datas[$bill_to_country]) ? $country_datas[$bill_to_country] : '',
                    'is_export_sales'            => $is_export_sales,
                    'warehouse_id'                => htmlentities($first_array['warehouse_id']),
                    'fulfillment_channel'        => $first_array['fulfillment_channel'],
                    'payment_method_code'        => htmlentities($payment_method_code),
                    'total_invoice_amount'        => number_format((float)$total_invoice_amount, 2, '.', ''),
                    'total_igst_tax'            => $total_igst_amount ? round($total_igst_amount, 2, PHP_ROUND_HALF_UP) : '',
                    'total_cgst_tax'            => $total_gst_amount <> 0 ? bcdiv($total_gst_amount, 2, 2) : '',
                    'total_sgst_tax'            => $total_gst_amount <> 0 ? bcdiv($total_gst_amount, 2, 2) : '',
                    // 'total_utgst_tax'			=> $first_array['utgst_tax'] != 0 ? bcdiv($total_tax,2,4) : '',
                    'item_promo_discount_basis'    => number_format((float)$total_item_promo_discount_basis, 2, '.', ''),
                    'gift_wrap_amount_basis'    => number_format((float)$total_gift_wrap_amount_basis, 2, '.', ''),
                    'gift_wrap_discount_basis'    => number_format((float)$total_gift_wrap_discount_basis, 2, '.', ''),
                    'shipping_amount_basis'        => number_format((float)$total_shipping_amount_basis, 2, '.', ''),
                    'shipping_promo_discount_basis'    => number_format((float)$total_shipping_promo_discount_basis, 2, '.', ''),
                    'cod_amount_basis'            => number_format((float)$total_cod_amount_basis, 2, '.', ''),
                    'cod_promo_discount_basis'    => number_format((float)$total_cod_promo_discount_basis, 2, '.', ''),
                    'einvoice_ack_no'              => (!empty($first_array['einvoice_ack_no']) ? $first_array['einvoice_ack_no'] : ''),
                    'einvoice_irn_no'              => (!empty($first_array['einvoice_irn_no']) ? $first_array['einvoice_irn_no'] : ''),
                    'einvoice_ack_date'            => $einvoice_ack_date,
                    'narration'                    => $narration,
                    'excel_inventories'            => array(),
                    'inventories'                => $inventories,
                    'tax_summary'                => isset($tax_summary) ? $tax_summary : ''
                );
            }

            if (COUNT($error_data)) { // add error in error sheet
                $comment = sprintf($this->language->get('error_wrong_gst_tax_count'), $first_array['reference'], $first_array['transaction_status'], count($error_data));
                $this->notification->add($comment, $customer_id);
                $this->addUploadError($error_data, $upload_id);
            }

            return $final_datas;
        } else {
            return false;
        }
    }
    public function getConsolidatedInvoiceForTallyXml($transaction_type, $upload_id, $customer_id, $user_state, $country_datas)
    {
        $this->load->language('auto/cron');

        $sql = "SELECT
                azi.az_id,azi.upload_id,azi.sales_type,azi.invoice_number,DATE_FORMAT(azi.invoice_date, '%Y%m%d') AS invoice_date,azi.invoice_amount,azi.transaction_type,azi.order_id,azi.order_date,azi.shipment_item_id,azi.quantity,azi.item_description,azi.sku,azi.asin,azi.product_note,azi.hsn_sac,azi.buyer_gst,azi.buyer_name,
                azb.customer_shipping_name,azb.customer_shipping_address,azb.ship_to_city,azb.ship_to_state,azb.ship_to_country,azb.ship_to_postal_code,azb.customer_billing_name,azb.customer_billing_address,azb.bill_to_city,azb.bill_to_state,azb.bill_to_country,azb.bill_to_postal_code,
                azs.shipping_amount_basis,azs.shipping_promo_discount_basis,azs.cod_amount_basis,azs.cod_promo_discount_basis,azs.dispatch_through,azs.tracking_number,azs.dispatch_date,azs.export_date,azs.export_bill_no,azs.export_port_code,
                azt.tax_exclusive_gross,azt.total_tax_amount,azt.cgst_tax,(azt.sgst_tax + azt.utgst_tax) AS sgst_tax,azt.igst_tax,azt.cgst_rate,(azt.sgst_rate + azt.utgst_rate) AS sgst_rate,azt.utgst_rate,azt.igst_rate,azt.principal_amount_basis,azt.item_promo_discount_basis,
                azg.gift_wrap_amount_basis,azg.gift_wrap_discount_basis,
                azo.warehouse_id,azo.fulfillment_channel,azo.credit_note_no,azo.credit_note_date,azo.einvoice_ack_no,azo.einvoice_irn_no,azo.einvoice_ack_date,
                u.usr AS usr, pm.tally_name AS product_tally_name, pm.hsn_sac as product_hsn_sac, cw.tally_name AS location_name, cw.address as warehouse_address, cw.city as warehouse_city, cw.pincode as warehouse_pincode
                    FROM " . DB_PREFIX . "az_invoice azi
                    JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id
                    JOIN " . DB_PREFIX . "az_shipping azs ON azs.az_id = azi.az_id
                    JOIN " . DB_PREFIX . "az_taxes azt ON azt.az_id = azi.az_id
                    JOIN " . DB_PREFIX . "az_giftwrap azg ON azg.az_id = azi.az_id
                    JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = azi.az_id
                    JOIN " . DB_PREFIX . "az_tcstax aztc ON aztc.az_id = azi.az_id
                    JOIN " . DB_PREFIX . "upload u ON u.upload_id = azi.upload_id AND u.upload_id = " . (int)$upload_id . "
                    JOIN " . DB_PREFIX . "product p ON p.asin = azi.asin AND p.channel_id = u.channel_id AND p.customer_id = u.usr
                    JOIN " . DB_PREFIX . "product_master pm ON pm.master_id = p.master_id AND p.customer_id = u.usr
                    JOIN " . DB_PREFIX . "channel_warehouse cw ON cw.code = azo.warehouse_id AND u.gst_id = cw.gst_id AND cw.customer_id = u.usr AND cw.channel_id = u.channel_id
                        WHERE azi.transaction_type = " . (int)$transaction_type . "";
        //$this->log->write($sql);
        $result = $this->db->query($sql);
        $result = $result->rows;

        $final_datas = array();
        $result_datas = array();
        $error_data = array();

        if (COUNT($result)) {
            // To Fetched Seller GSTN
            $query = $this->db->query("SELECT gst_number FROM " . DB_PREFIX . "gst g
                                        JOIN " . DB_PREFIX . "upload u ON u.upload_id = " . (int)$upload_id . " AND u.gst_id = g.gst_id");
            $seller_gstin = $query->row['gst_number'];

            $ship_from_query = $this->db->query("SELECT 
                    s.name as state_name, g.gst_number
                FROM " . DB_PREFIX . "state s
                JOIN " . DB_PREFIX . "upload u ON u.upload_id = " . (int)$upload_id . "
                JOIN " . DB_PREFIX . "gst g ON g.gst_id = u.gst_id AND g.customer_id = u.usr
                WHERE s.tin_code = LEFT(g.gst_number,2)");
            $ship_from_state = $ship_from_query->row['state_name'];
            $seller_gstin = $ship_from_query->row['gst_number'];

            // To fetched status name
            $query = $this->db->query("SELECT status_id, name
                                        FROM " . DB_PREFIX . "status
                                        WHERE status_id IN(" . implode(',', array_column($result, 'transaction_type')) . ")");
            $status_info = array_column($query->rows, 'name', 'status_id');

            $min_invoice_date = min(array_column($result, 'invoice_date'));
            $state_info = $this->getStateDatas($min_invoice_date);

            //#KM potter consolidated invoice grouping ********

            $upload_data = $this->getUploadData($upload_id);
            if ($upload_data) {
                $channel_code = $this->db->query("SELECT COALESCE((SELECT channel_code FROM " . DB_PREFIX . "channel WHERE channel_id = " . $upload_data['channel_id'] . " LIMIT 1), '') AS channel_code")->row['channel_code'];
            }

            //if ($upload_id==102375) {

                static $group_totals = array();
                static $group_numbers = array();
                
            //}
            
            //#KM potter consolidated invoice grouping ********

        
            //#KM potter B2B to B2C 20250602

            // if ($customer_id==312) {

            //     // Filter array for B2B sales in India and get list of unique GST numbers
            //     $b2b_india_records = array_filter($result, function($item) {
            //         return isset($item['sales_type']) && isset($item['ship_to_country']) && 
            //             $item['sales_type'] == 'B2B' && $item['ship_to_country'] == 'IN' && 
            //             !empty($item['buyer_gst']);
            //     });
                
            //     // Extract unique GST numbers from filtered records
            //     $unique_b2b_gst_numbers = array_unique(array_filter(array_column($b2b_india_records, 'buyer_gst')));
            //     $invalid_gst_numbers = array();

            //     // Validate each unique GST number
            //     foreach ($unique_b2b_gst_numbers as $gst_number) {
            //         // Call API to validate GST number
            //         //$request = array('action' => 'getGSTINDetails', 'gstinno' => $gst_number, 'debug' => false);
            //         //$api_response = $this->einv->taxproei($request, 202, 132);
            //         $request = array('gst_number' => $gst_number);
            //         $service_type = array('path' => '/public/search', 'method' => 'get');
            //         $api_datas = $this->gst->mastergst($request, $service_type, 0, $customer_id);
            //         //$this->log->write("API Response: " . print_r($api_datas, true));
            //         // Check if API response contains data
            //         if (isset($api_datas['data'])) {
            //             $responseData = is_array($api_datas['data']) ? 
            //                         $api_datas['data'] : 
            //                         json_decode($api_datas['data'], true);
                        
            //             // Mark as invalid if status is not active
            //             if (isset($responseData['sts']) && $responseData['sts'] != 'Active') {
            //                 $invalid_gst_numbers[] = $gst_number;
            //             }
            //         } else {
            //             // If API fails, consider GST as invalid
            //             $invalid_gst_numbers[] = $gst_number;
            //         }
            //     }
                
            //     // Log invalid GST numbers
            //     if (count($invalid_gst_numbers)) {
            //         $this->log->write("Invalid GST numbers: " . print_r($invalid_gst_numbers, true));
            //     }

            //     // Update sales type to B2C for records with invalid GST numbers
            //     foreach ($result as $key => $res) {
            //         if (isset($res['buyer_gst']) && in_array($res['buyer_gst'], $invalid_gst_numbers)) {
            //             $result[$key]['sales_type'] = 'B2C';
                        
            //             // Add notification for invalid GST
            //             $invalid_gst_comment = sprintf($this->language->get('notification_invalid_gst_not_found'), $upload_data['reference'], $res['invoice_number'], $res['buyer_gst']);
            //             $this->notification->add($invalid_gst_comment, $customer_id);


            //         }
            //     }
            
            // }   

            //#KM potter B2B to B2C 20250602



            foreach ($result as $res) {
                if ($res['sales_type'] == 'B2C' && $res['ship_to_country'] == 'IN') {

                    //#KM potter consolidated invoice grouping ********
                    //if ($upload_id==102375) {
                       
                        // Debug: Log initial invoice details
                        //$this->log->write("Processing invoice: " . $res['invoice_number'] . ", Order ID: " . $res['order_id'] . ", Amount: " . $res['invoice_amount']);
                        
                        // Create a unique key for each invoice to ensure items from same invoice stay together
                        $invoice_key = $res['invoice_number'] . '_' . $res['order_id'];
                        
                        // Map invoice to group to ensure same invoice numbers don't appear in different groups
                        static $invoice_to_group_map = array();
                        
                        // If this invoice already has a group assigned, use that group
                        if (isset($invoice_to_group_map[$invoice_key])) {
                            $group_key = $invoice_to_group_map[$invoice_key];
                            //$this->log->write("Using existing group: " . $group_key);
                            //$this->log->write("Invoice to group map: " . print_r($invoice_to_group_map, true));
                            // Check if adding this invoice would exceed threshold
                            // Only check for first item of an invoice to keep all items together
                            $new_total = $group_totals[$group_key] + abs($res['invoice_amount']);
                            //$this->log->write("Current group total: " . $group_totals[$group_key] . ", New total would be: " . $new_total);
                            
                            if ($new_total > 49500) {
                                $group_numbers[$group_key]++;
                                $group_totals[$group_key] = abs($res['invoice_amount']);
                                $invoice_to_group_map[$invoice_key] = $group_key;

                                //$this->log->write("Exceeded threshold, creating new group number: " . $group_numbers[$group_key]);
                                
                            } else {
                                $group_totals[$group_key] = $new_total;
                                //$this->log->write("Within threshold, updated group total: " . $group_totals[$group_key]);
                            }
                        } else {
                            // Otherwise create a new group key based on transaction type, sales type and state
                            
                            $month_year = date($this->language->get('datetime_format_monthyear'), strtotime($res['invoice_date']));
                            $group_key = $res['transaction_type'] . '_' . $res['sales_type'] . '_' . strtolower($res['ship_to_state']) . '_' . $month_year;
                            //$this->log->write("Creating new group: " . $group_key);
                            
                            // Initialize group if not exists
                            if (!isset($group_totals[$group_key])) {
                                $group_totals[$group_key] = 0;
                                $group_numbers[$group_key] = 1;
                                //$this->log->write("Initializing new group totals: " . $group_key);
                            }
                            
                            // Check if adding this invoice would exceed threshold
                            // Only check for first item of an invoice to keep all items together
                            $new_total = $group_totals[$group_key] + abs($res['invoice_amount']);
                            //$this->log->write("Current group total: " . $group_totals[$group_key] . ", New total would be: " . $new_total);
                            
                            if ($new_total > 49500) {
                                $group_numbers[$group_key]++;
                                $group_totals[$group_key] = abs($res['invoice_amount']);
                                //$this->log->write("Exceeded threshold, creating new group number: " . $group_numbers[$group_key]);
                            } else {
                                $group_totals[$group_key] = $new_total;
                                //$this->log->write("Within threshold, updated group total: " . $group_totals[$group_key]);
                            }
                            
                            // Map this invoice to its assigned group
                            $invoice_to_group_map[$invoice_key] = $group_key;
                            //$this->log->write("Mapped invoice " . $invoice_key . " to group " . $group_key);
                        }
                        
                        // Add group number to invoice number
                        // Generate a 3-digit zero-padded number from the group number
                        // This ensures consistent formatting for invoice postfixes
                        $invoice_postfix = str_pad($group_numbers[$group_key], 3, '0', STR_PAD_LEFT);
                        //$this->log->write("Generated invoice postfix: " . $invoice_postfix);
                        

                    //}
                    //#KM potter consolidated invoice grouping ********

                    switch ($res['transaction_type']) {
                        case 7:
                            $invoice_prefix = '';
                            break;
                        case 9:
                            continue 2;
                            break; // Cancelled invoice skip if consolidated entry
                        case 8:
                            $invoice_prefix = 'C-';
                            break;
                        case 10:
                            $invoice_prefix = 'D-';
                            break;
                    }
                    // Other Peramters Used to create csv file START
                    $res['original_invoice_number'] = $res['invoice_number'];
                    $res['original_invoice_date'] = $res['invoice_date'];
                    $res['original_order_id'] = $res['order_id'];
                    $res['original_order_date'] = $res['order_date'];
                    $res['status_name'] = $status_info[$res['transaction_type']];
                    // Other Peramters Used to create csv file END

                    $state_code = $state_info[strtolower($res['ship_to_state'])];
                    $month_year = date($this->language->get('datetime_format_monthyear'), strtotime($res['invoice_date']));
                    $res['invoice_date'] = date($this->language->get('datetime_format_last_day'), strtotime($res['invoice_date']));
                    $res['order_date'] = date($this->language->get('datetime_format_last_day'), strtotime($res['order_date']));
                    $res['state_code'] = $state_code;

                    //#KM potter consolidated invoice grouping ********
                    // if ($upload_id==102375) {
                      
                        //$res['invoice_number'] = $invoice_prefix . $state_code . '/' . $month_year;
                        $res['invoice_number'] = $channel_code . $invoice_prefix . $state_code . $month_year . $invoice_postfix;

                    // } else {
                    //     $res['invoice_number'] = $invoice_prefix . $state_code . '/' . $month_year;
                    // }
                    //#KM potter consolidated invoice grouping ********
                    
                    $remove_key = array('order_id', 'shipment_item_id', 'product_note', 'hsn_sac', 'dispatch_through', 'tracking_number', 'dispatch_date', 'customer_billing_name', 'customer_billing_address', 'bill_to_city', 'bill_to_postal_code', 'export_date', 'export_bill_no', 'export_port_code', 'credit_note_date', 'credit_note_no');
                } elseif ($res['sales_type'] == 'B2B' && $res['ship_to_country'] == 'IN') {
                    // Other Peramters Used to create csv file START
                    $res['original_invoice_number'] = $res['invoice_number'];
                    $res['original_invoice_date'] = $res['invoice_date'];
                    $res['original_order_id'] = $res['order_id'];
                    $res['original_order_date'] = $res['order_date'];
                    $res['status_name'] = $status_info[$res['transaction_type']];
                    // Other Peramters Used to create csv file END
                    $remove_key = array('product_note', 'export_date', 'export_bill_no', 'export_port_code');

                } else { // Export Sales
                    // Other Peramters Used to create csv file START
                    $res['original_invoice_number'] = $res['invoice_number'];
                    $res['original_invoice_date'] = $res['invoice_date'];
                    $res['original_order_id'] = $res['order_id'];
                    $res['original_order_date'] = $res['order_date'];
                    $res['status_name'] = $status_info[$res['transaction_type']];
                    // Other Peramters Used to create csv file END
                    $remove_key = array();
                }

                $temp_array_key = $res['transaction_type'] . $res['sales_type'] . strtolower($res['ship_to_state']) . $res['invoice_number'];
                $result_datas[$temp_array_key][] = array_diff_key($res, array_flip($remove_key));
            }
        }

        $module_consolidated_entry = $this->modules->status('consolidated_entry', $customer_id);
        $module_sales_gst_ledger = $this->modules->status('sales_gst_ledger', $customer_id); // GST Rate wise ledger for Sales Account, IGST, CGST, SGST

        //#KM potter consolidated invoice grouping ********
        //if ($upload_id==102375) {
            //$this->log->write('Result Datas:');
            //$this->log->write($result_datas);

           
            $az_ids = [];
            $invoice_updates = [];

            foreach ($result_datas as $temp_key => $group) {
                //$this->log->write('Processing group with key: ' . $temp_key);
                // $this->log->write('Group data: ' . print_r($group, true));
                // $this->log->write('First item in group: ' . print_r(reset($group), true));
                $new_group = reset($group);
                // Only process B2C sales in India
                if (isset($new_group['sales_type']) && isset($new_group['ship_to_country']) && $new_group['sales_type'] === 'B2C' && $new_group['ship_to_country'] === 'IN') {
                    //foreach ($new_group as $item) {
                    foreach ($group as $key => $item) { 
                        //$this->log->write('Processing item id : ' . print_r($item['az_id'], true));
                        // $this->log->write('Processing item invoice_number: ' . print_r($item['invoice_number'], true));
                        if (isset($item['az_id']) && isset($item['invoice_number'])) {
                            $az_id = (int)$item['az_id'];
                            $invoice_number = trim($item['invoice_number']);
                            
                            if ($az_id > 0 && !empty($invoice_number)) {
                                $az_ids[] = $az_id;
                                // Store updates in a way that allows batching
                                $invoice_updates[$az_id] = $invoice_number;
                            }
                        }
                    }
                }
            }

            // $this->log->write('Number of records to update: ' . count($az_ids));
            // $this->log->write('AZ IDs to update: ' . print_r($az_ids, true));
            //$this->log->write('Invoice Updates: ' . print_r($invoice_updates, true));

            
            if (!empty($az_ids)) {
                try {
                    // Log start time
                    $start_time = microtime(true);
                    $this->log->write('Starting consolidated invoice no update query at: ' . date('Y-m-d H:i:s'));

                    // Construct a single batch UPDATE query
                    $sql = "UPDATE " . DB_PREFIX . "az_invoice SET consolidated_invoice_no = CASE ";
                    foreach ($invoice_updates as $az_id => $invoice_number) {
                        $sql .= " WHEN az_id = " . (int)$az_id . " THEN '" . $this->db->escape($invoice_number) . "'";
                    }
                    $sql .= " END WHERE az_id IN (" . implode(',', $az_ids) . ")";

                    $this->db->query($sql);
                    //$this->log->write('Executed query: ' . $sql);
                    // Log end time and calculate duration
                    $end_time = microtime(true);
                    $execution_time = ($end_time - $start_time);
                    $this->log->write('consolidated invoice no completed at: ' . date('Y-m-d H:i:s') . ' - Execution time: ' . number_format($execution_time, 4) . ' seconds');
                    

                } catch (Exception $e) {
                    $this->log->write('Error updating invoice numbers: ' . $e->getMessage());
                }
            }

        //}
        //#KM potter consolidated invoice grouping ********


        if (COUNT($result_datas)) {
            $tally_version = $this->customersetting->get('general_default_tally_version', $customer_id);

            foreach ($result_datas as $temp_key => $query) {
                $inventories = array();
                $excel_inventories = array();
                $tax_summary = array();

                $first_array = reset($query);

                if ($first_array['sales_type'] == 'B2C' && $first_array['ship_to_country'] == 'IN') {
                    $consolidated = true;
                } elseif ($first_array['sales_type'] == 'B2B' && $first_array['ship_to_country'] == 'IN') {
                    $consolidated = false;
                } else { // Export
                    $consolidated = false;
                }

                if ($transaction_type == 8) {
                    if ($first_array['sales_type'] == 'B2C') {
                        $narration = sprintf($this->language->get('text_narration_title_return_retail_consolidated'), $first_array['ship_to_state']);
                    } elseif ($first_array['sales_type'] == 'B2B') {
                        $narration = sprintf($this->language->get('text_narration_title_return_business'), $query[0]['order_id']);
                    }
                } else {
                    if ($first_array['sales_type'] == 'B2C') {
                        $narration = sprintf($this->language->get('text_narration_title_sales_retail_consolidated'), $first_array['ship_to_state']);
                    } elseif ($first_array['sales_type'] == 'B2B') {
                        $narration = sprintf($this->language->get('text_narration_title_sales_business'), $query[0]['order_id']);
                    }
                }

                $remove_key = array('az_id', 'upload_id', 'sales_type', 'invoice_number', 'invoice_date', 'transaction_type', 'order_date', 'state_code', 'customer_billing_name', 'customer_billing_address', 'bill_to_city', 'bill_to_state', 'bill_to_country', 'bill_to_postal_code', 'credit_note_no', 'credit_note_date', 'usr');

                // Totals
                $total_invoice_amount = round(array_sum(array_column($query, 'invoice_amount')), 2);
                $total_tax = array_sum(array_column($query, 'total_tax_amount'));
                $total_item_promo_discount_basis = array_sum(array_column($query, 'item_promo_discount_basis'));
                $total_gift_wrap_amount_basis = array_sum(array_column($query, 'gift_wrap_amount_basis'));
                $total_gift_wrap_discount_basis = array_sum(array_column($query, 'gift_wrap_discount_basis'));
                $total_shipping_amount_basis = array_sum(array_column($query, 'shipping_amount_basis'));
                $total_shipping_promo_discount_basis = array_sum(array_column($query, 'shipping_promo_discount_basis'));
                $total_cod_amount_basis = array_sum(array_column($query, 'cod_amount_basis'));
                $total_cod_promo_discount_basis = array_sum(array_column($query, 'cod_promo_discount_basis'));

                $total_local_tax = 0;
                $total_interstate_tax = 0;

                // Inventories Section
                foreach ($query as $key => $q) {
                    // Tax Total START
                    if ($q['igst_rate'] <> 0) {
                        $total_interstate_tax += $q['total_tax_amount'];
                    } elseif ($q['cgst_rate'] <> 0) {
                        $total_local_tax += $q['total_tax_amount'];
                    }
                    // Tax Total END

                    if ($module_consolidated_entry) { // This is only available for consolidated entry
                        $excel_inventories[$key] = array_diff_key($q, array_flip($remove_key));
                        $excel_inventories[$key]['item_description'] = html_entity_decode($q['item_description']);
                        $excel_inventories[$key]['product_rate_per_piece'] = bcdiv($q['principal_amount_basis'], $q['quantity'], 2);
                        $excel_inventories[$key]['customer_shipping_name'] = html_entity_decode($this->encrypt->pDecrypt($q['customer_shipping_name']));
                        $excel_inventories[$key]['customer_shipping_address'] = html_entity_decode($this->encrypt->pDecrypt($q['customer_shipping_address']));
                        $excel_inventories[$key]['ship_to_city'] = html_entity_decode($q['ship_to_city']);
                        $excel_inventories[$key]['ship_to_state'] = html_entity_decode($q['ship_to_state']);
                        if ($q['igst_rate'] <> 0) {
                            $excel_inventories[$key]['igst_tax'] = $q['total_tax_amount'];
                        } elseif ($q['cgst_rate'] <> 0) {
                            $excel_inventories[$key]['cgst_tax'] = bcdiv($q['total_tax_amount'], 2, 2);
                            $excel_inventories[$key]['sgst_tax'] = bcdiv($q['total_tax_amount'], 2, 2);
                        }
                    }

                    if ($module_consolidated_entry && $q['sales_type'] == 'B2C' && $q['ship_to_country'] == 'IN') { // Make unique entry
                        $key = $q['product_tally_name'] . $q['location_name'] . ($q['igst_rate'] + $q['cgst_rate'] + $q['sgst_rate']);
                    }
                    !isset($inventory_total[$key]) ? $inventory_total[$key] = array() : '';

                    $inventories[$key] = array_diff_key($q, array_flip($remove_key));

                    if ($q['cgst_rate'] <> 0) { // Unsed only for local sales within State
                        $total_tax_rate = array_sum(array($q['cgst_rate'], $q['sgst_rate'])); // Getting 0% Tax Rate in GSTR-1 for that igst rate added in tax rate

                        switch ($tally_version) {
                            case 'primev3':
                            case 'primev4':
                            case 'primev5':
                            case 'primev6':    
                                $inventories[$key]['tax_rate'] = sprintf($this->language->get('text_gst_tax_rate_igst_v3'), $total_tax_rate) . sprintf($this->language->get('text_gst_tax_rate_cgst_v3'), $q['cgst_rate']) . sprintf($this->language->get('text_gst_tax_rate_sgst_v3'), bcadd($q['utgst_rate'], $q['sgst_rate'], 2)) . $this->language->get('text_gst_tax_rate_cess_v3') . $this->language->get('text_gst_tax_rate_state_cess_v3');

                                $inventories[$key]['taxable_sales'] = 'Local Sales - Taxable';
                                $inventories[$key]['gst_taxability_type'] = 'Taxable';
                                break;
                            default:
                                $inventories[$key]['tax_rate'] = sprintf($this->language->get('text_gst_tax_rate_igst'), $total_tax_rate) . sprintf($this->language->get('text_gst_tax_rate_cgst'), $q['cgst_rate']) . ($q['utgst_rate'] <> 0 ? sprintf($this->language->get('text_gst_tax_rate_utgst'), $q['utgst_rate']) : sprintf($this->language->get('text_gst_tax_rate_sgst'), $q['sgst_rate']));

                                $inventories[$key]['taxable_sales'] = 'Sales Taxable';
                                $inventories[$key]['gst_taxability_type'] = 'Taxable';
                                break;
                        }
                    } elseif ($q['igst_rate'] <> 0) {
                        switch ($tally_version) {
                            case 'primev3':
                            case 'primev4':
                            case 'primev5':
                            case 'primev6':    
                                $inventories[$key]['tax_rate'] = sprintf($this->language->get('text_gst_tax_rate_igst_v3'), $q['igst_rate']) . $this->language->get('text_gst_tax_rate_sgst_blank_v3') . $this->language->get('text_gst_tax_rate_cgst_blank_v3') . $this->language->get('text_gst_tax_rate_cess_v3') . $this->language->get('text_gst_tax_rate_state_cess_v3');

                                // Export Sales | Sales within India
                                $inventories[$key]['taxable_sales'] = ($first_array['ship_to_country'] <> 'IN') ? 'Exports - Taxable' : 'Interstate Sales - Taxable';
                                $inventories[$key]['gst_taxability_type'] = 'Taxable';
                                break;
                            default:
                                $inventories[$key]['tax_rate'] = sprintf($this->language->get('text_gst_tax_rate_igst'), $q['igst_rate']) . $this->language->get('text_gst_tax_rate_sgst_blank') . $this->language->get('text_gst_tax_rate_cgst_blank');


                                // Export Sales | Sales within India
                                $inventories[$key]['taxable_sales'] = ($first_array['ship_to_country'] <> 'IN') ? 'Exports Taxable' : 'Interstate Sales Taxable';
                                $inventories[$key]['gst_taxability_type'] = 'Taxable';

                                break;
                        }
                    } else { // Common Blank Tax Rate for Nill Rated Sales
                        switch ($tally_version) {
                            case 'primev3':
                            case 'primev4':
                            case 'primev5':
                            case 'primev6':    
                                $inventories[$key]['tax_rate'] = $this->language->get('text_gst_tax_rate_igst_blank_v3') . $this->language->get('text_gst_tax_rate_cgst_blank_v3') . $this->language->get('text_gst_tax_rate_sgst_blank_v3') . $this->language->get('text_gst_tax_rate_cess_v3') . $this->language->get('text_gst_tax_rate_state_cess_v3');

                                if ($first_array['ship_to_country'] != 'IN') { // Export Sales nil rated
                                    $inventories[$key]['taxable_sales'] = 'Exports - Exempt';
                                    $inventories[$key]['gst_taxability_type'] = 'Exempt';
                                } elseif ($user_state == strtolower($first_array['ship_to_state'])) { // Local sales nil rated
                                    $inventories[$key]['taxable_sales'] = 'Local Sales - Nil Rated';
                                    $inventories[$key]['gst_taxability_type'] = 'Nil Rated';
                                } else { // Interstate sales nil rated
                                    $inventories[$key]['taxable_sales'] = 'Interstate Sales - Nil Rated';
                                    $inventories[$key]['gst_taxability_type'] = 'Nil Rated';
                                }
                                break;
                            default:
                                $inventories[$key]['tax_rate'] = $this->language->get('text_gst_tax_rate_igst_blank') . $this->language->get('text_gst_tax_rate_cgst_blank') . $this->language->get('text_gst_tax_rate_sgst_blank');

                                if ($first_array['ship_to_country'] != 'IN') { // Export Sales nil rated
                                    $inventories[$key]['taxable_sales'] = 'Exports Nil Rated';
                                    $inventories[$key]['gst_taxability_type'] = 'Exempt';
                                } elseif ($user_state == strtolower($first_array['ship_to_state'])) { // Local sales nil rated
                                    $inventories[$key]['taxable_sales'] = 'Sales Nil Rated';
                                    $inventories[$key]['gst_taxability_type'] = 'Nil Rated';
                                } else { // Interstate sales nil rated
                                    $inventories[$key]['taxable_sales'] = 'Interstate Sales Nil Rated';
                                    $inventories[$key]['gst_taxability_type'] = 'Nil Rated';
                                }
                                break;
                        }
                    }

                    // Individual Product Narration
                    if ($consolidated) {
                        $inventories[$key]['product_note'] = '';
                    } else {
                        $inventories[$key]['product_note'] = $first_array['ship_to_country'] <> 'IN' ? $inventories[$key]['product_note'] : $inventories[$key]['sku'];
                    }
                    if (strpos($inventories[$key]['product_tally_name'], '&amp;') !== false) {
                        $inventories[$key]['product_tally_name'] = str_replace('&amp;', '&', $inventories[$key]['product_tally_name']);
                    }
                    if (strpos($inventories[$key]['product_tally_name'], '&quot;') !== false) {
                        $inventories[$key]['product_tally_name'] = str_replace('&quot;', '"', $inventories[$key]['product_tally_name']);
                    }
                    $inventories[$key]['product_tally_name'] = htmlentities($inventories[$key]['product_tally_name']); // htmlentities used because if user upload tally product name in special charachter

                    // Add tax rate of cess and cess on qty only in debit note
                    // if ($first_array['transaction_type'] == 10) {
                    //     $inventories[$key]['tax_rate'] .= $this->language->get('text_gst_tax_rate_cess') . $this->language->get('text_gst_tax_rate_cess_on_qty');
                    // }

                    if (isset($inventory_total[$key]['quantity'])) {
                        $inventory_total[$key]['quantity'] += $q['quantity'];
                    } else {
                        $inventory_total[$key]['quantity'] = $q['quantity'];
                    }
                    if (isset($inventory_total[$key]['principal_amount_basis'])) {
                        $inventory_total[$key]['principal_amount_basis'] += $q['principal_amount_basis'];
                    } else {
                        $inventory_total[$key]['principal_amount_basis'] = $q['principal_amount_basis'];
                    }

                    if ($first_array['sales_type'] <> 'B2C' || $first_array['ship_to_country'] <> 'IN') { // B2B OR Exports
                        $narration .= sprintf($this->language->get('text_narration_data'), htmlentities($q['item_description']), $q['quantity']);
                    }

                    if ($module_sales_gst_ledger) {
                        $sgst_rate = $inventories[$key]['sgst_rate'];
                        $cgst_rate = $inventories[$key]['cgst_rate'];
                        $igst_rate = $inventories[$key]['igst_rate'];
                        // $utgst_rate = $inventories[$key]['utgst_rate'];
                        // if Sales And GST Tax Rate Module on then run else blank array
                        if ($cgst_rate <> 0) {
                            if (!isset($tax_summary['cgst'][$cgst_rate])) {
                                $tax_summary['cgst'][$cgst_rate] = 0;
                            }
                            $tax_summary['cgst'][$cgst_rate] += bcdiv($inventories[$key]['total_tax_amount'], 2, 4);
                            $tax_summary['cgst'][$cgst_rate] = round($tax_summary['cgst'][$cgst_rate], 3, PHP_ROUND_HALF_UP);
                        }
                        if ($sgst_rate <> 0) {
                            if (!isset($tax_summary['sgst'][$sgst_rate])) {
                                $tax_summary['sgst'][$sgst_rate] = 0;
                            }
                            $tax_summary['sgst'][$sgst_rate] += bcdiv($inventories[$key]['total_tax_amount'], 2, 4);
                            $tax_summary['sgst'][$sgst_rate] = round($tax_summary['sgst'][$sgst_rate], 3, PHP_ROUND_HALF_UP);
                        }
                        // if($utgst_rate <> 0){
                        // 	if(!isset($tax_summary['utgst'][$utgst_rate])){$tax_summary['utgst'][$utgst_rate]=0;}
                        // 	$tax_summary['utgst'][$utgst_rate] += bcdiv($inventories[$key]['total_tax_amount'],2,4);
                        //  $tax_summary['utgst'][$utgst_rate] = round($tax_summary['utgst'][$utgst_rate], 2, PHP_ROUND_HALF_UP);
                        // }
                        if ($igst_rate <> 0) {
                            if (!isset($tax_summary['igst'][$igst_rate])) {
                                $tax_summary['igst'][$igst_rate] = 0;
                            }
                            $tax_summary['igst'][$igst_rate] += $inventories[$key]['total_tax_amount'];
                            $tax_summary['igst'][$igst_rate] = round($tax_summary['igst'][$igst_rate], 3, PHP_ROUND_HALF_UP);
                        }
                    }

                    $inventories[$key]['customer_shipping_name'] = $this->encrypt->pDecrypt($q['customer_shipping_name']);
                    $inventories[$key]['customer_shipping_address'] = $this->encrypt->pDecrypt($q['customer_shipping_address']);
                }

                // For calculate product_rate_per_piece
                foreach ($inventories as $key => $inventory) {
                    $inventories[$key]['product_rate_per_piece'] = bcdiv($inventory_total[$key]['principal_amount_basis'], $inventory_total[$key]['quantity'], 2);
                    $inventories[$key]['quantity'] = $inventory_total[$key]['quantity'];
                    $inventories[$key]['principal_amount_basis'] = round($inventory_total[$key]['principal_amount_basis'], 2, PHP_ROUND_HALF_UP);
                }
                unset($inventory_total);


                if ($first_array['sales_type'] == 'B2C') {
                    $sales_type = 'Consumer';
                } elseif ($first_array['sales_type'] == 'B2B') {
                    $sales_type = 'Regular';
                }

                if ($first_array['transaction_type'] == 8) {
                    $nature_of_return = '01-Sales Return';

                    switch ($tally_version) {
                        case 'primev3':
                        case 'primev4':
                        case 'primev5':
                        case 'primev6':    
                            if (abs($total_invoice_amount) < 100000) {
                                $original_sale_value = 'Less Than or Equal to 1 Lakhs';
                                $ord_sale_value = 'B2C (Small)'; //KM #potter 20250429
                            } else {
                                $original_sale_value = 'More Than 1 Lakhs';
                                $ord_sale_value = 'B2C (Large)'; //KM #potter 20250429
                            }
                            break;
                        default:
                            if (abs($total_invoice_amount) < 250000) {
                                $original_sale_value = 'Lesser than or equal to 2.5 lakhs';
                                $ord_sale_value = 'B2C (Small)'; //KM #potter 20250429
                            } else {
                                $original_sale_value = 'More than or equal to 2.5 lakhs';
                                $ord_sale_value = 'B2C (Large)'; //KM #potter 20250429
                            }
                            break;
                    }

                    if (!empty($first_array['credit_note_no'])) {
                        $bill_type = 'Agst Ref';
                    } else {
                        $first_array['credit_note_no'] = $first_array['invoice_number'];
                        $bill_type = 'New Ref';
                    }
                } else {
                    $first_array['credit_note_no'] = $first_array['invoice_number'];
                    $bill_type = 'New Ref';
                }

                if ($first_array['cgst_rate'] <> 0) {
                    $taxable_sales = 'Sales Taxable';

                    switch ($tally_version) {
                        case 'primev3':
                        case 'primev4':
                        case 'primev5':
                        case 'primev6':    
                            $tax_rate = $this->language->get('text_gst_tax_rate_igst_blank_v3') . "\n" . sprintf($this->language->get('text_gst_tax_rate_cgst_v3'), $first_array['cgst_rate']) . "\n" . sprintf($this->language->get('text_gst_tax_rate_sgst_v3'), bcadd($first_array['utgst_rate'], $first_array['sgst_rate'], 2)) . $this->language->get('text_gst_tax_rate_cess_v3') . $this->language->get('text_gst_tax_rate_state_cess_v3');
                            break;
                        default:
                            $tax_rate = $this->language->get('text_gst_tax_rate_igst_blank') . "\n" . sprintf($this->language->get('text_gst_tax_rate_cgst'), $first_array['cgst_rate']) . "\n" . ($first_array['utgst_rate'] <> 0 ? sprintf($this->language->get('text_gst_tax_rate_utgst'), $first_array['utgst_rate']) : sprintf($this->language->get('text_gst_tax_rate_sgst'), $first_array['sgst_rate']));
                            break;
                    }
                } elseif ($first_array['igst_rate'] <> 0) {
                    switch ($tally_version) {
                        case 'primev3':
                        case 'primev4':
                        case 'primev5':
                        case 'primev6':    
                            $tax_rate = sprintf($this->language->get('text_gst_tax_rate_igst_v3'), $first_array['igst_rate']) . "\n" . $this->language->get('text_gst_tax_rate_cgst_blank_v3') . "\n" . $this->language->get('text_gst_tax_rate_sgst_blank_v3') . $this->language->get('text_gst_tax_rate_cess_v3') . $this->language->get('text_gst_tax_rate_state_cess_v3');
                            break;
                        default:
                            $tax_rate = sprintf($this->language->get('text_gst_tax_rate_igst'), $first_array['igst_rate']) . "\n" . $this->language->get('text_gst_tax_rate_cgst_blank') . "\n" . $this->language->get('text_gst_tax_rate_sgst_blank');
                            break;
                    }

                    // Export Sales nil rated
                    $taxable_sales = ($first_array['ship_to_country'] <> 'IN') ? 'Exports Taxable' : 'Interstate Sales Taxable';
                } else {
                    switch ($tally_version) {
                        case 'primev3':
                        case 'primev4':
                        case 'primev5':
                        case 'primev6':    
                            $tax_rate = $this->language->get('text_gst_tax_rate_igst_blank_v3') . "\n" . $this->language->get('text_gst_tax_rate_cgst_blank_v3') . "\n" . $this->language->get('text_gst_tax_rate_sgst_blank_v3') . $this->language->get('text_gst_tax_rate_cess_v3') . $this->language->get('text_gst_tax_rate_state_cess_v3');
                            break;
                        default:
                            $tax_rate = $this->language->get('text_gst_tax_rate_igst_blank') . "\n" . $this->language->get('text_gst_tax_rate_cgst_blank') . "\n" . $this->language->get('text_gst_tax_rate_sgst_blank');
                            break;
                    }

                    if ($first_array['ship_to_country'] <> 'IN') { // Export Sales nil rated
                        $taxable_sales = 'Exports Nil Rated';
                    } elseif ($user_state == strtolower($first_array['ship_to_state'])) { // Local sales nil rated
                        $taxable_sales = 'Sales Nil Rated';
                    } else { // Interstate sales nil rated
                        $taxable_sales = 'Interstate Sales Nil Rated';
                    }
                }

                if (isset($first_array['payment_method_code'])) {
                    $payment_methods = $this->getPaymentMethod();

                    foreach ($payment_methods as $method) {
                        if (strtolower($method['search']) == strtolower($first_array['payment_method_code'])) {
                            $method_code = $method['name'];
                            break;
                        }
                    }

                    if (isset($method_code)) {
                        $payment_method_code = $method_code;
                    } else {
                        $payment_method_code = $first_array['payment_method_code'];
                    }
                } else {
                    $payment_method_code = '';
                }

                //////// End Here Customer address break in part, add its city and pincode details And buyer phone and email in single variable
                // Customer Shipping Address
                if (isset($first_array['customer_shipping_address']) && !$consolidated) {
                    $customer_shipping_address = !empty(TRIM($first_array['customer_shipping_address'])) ? $this->encrypt->pDecrypt($first_array['customer_shipping_address']) : '';
                    $customer_address_basic = $customer_shipping_address;
                    if (strlen($customer_shipping_address) > 55) {
                        $shipping_address_string = wordwrap($customer_shipping_address, 55, "~~", false);
                        $shipping_address_string = explode('~~', $shipping_address_string);

                        $customer_shipping_address = '';
                        $customer_address_basic = '';
                        foreach ($shipping_address_string as $address_string) {
                            $address_string = $this->limitedHtmlEntitiesConvert($address_string);
                            // if (($transaction_type == 7) or ($transaction_type == 10)) {
                            $customer_shipping_address .= '<ADDRESS>' . $address_string . '</ADDRESS>';
                            $customer_address_basic .= '<BASICBUYERADDRESS>' . $address_string . '</BASICBUYERADDRESS>';
                            // } elseif ($transaction_type == 8) {
                            //     $customer_shipping_address .= '<BASICBUYERADDRESS>' . $address_string . '</BASICBUYERADDRESS>';
                            //     $customer_address_basic .= '<BASICBUYERADDRESS>' . $address_string . '</BASICBUYERADDRESS>';
                            // }
                        }
                    } elseif (!empty($customer_shipping_address)) { // Avoid blank address entry
                        $customer_shipping_address = $this->limitedHtmlEntitiesConvert($customer_shipping_address);
                        // if (($transaction_type == 7) or ($transaction_type == 10)) {
                        $customer_shipping_address = '<ADDRESS>' . $customer_shipping_address . '</ADDRESS>';
                        $customer_address_basic = '<BASICBUYERADDRESS>' . $customer_shipping_address . '</BASICBUYERADDRESS>';
                        // } elseif ($transaction_type == 8) {
                        //     $customer_shipping_address = '<BASICBUYERADDRESS>' . $customer_shipping_address . '</BASICBUYERADDRESS>';
                        //     $customer_address_basic = '<BASICBUYERADDRESS>' . $customer_shipping_address . '</BASICBUYERADDRESS>';
                        // }
                    } else {
                        $customer_shipping_address = '';
                        $customer_address_basic = '';
                    }

                    $ship_to_city = !empty($first_array['ship_to_city']) ? $this->limitedHtmlEntitiesConvert($first_array['ship_to_city']) . ', ' : '';
                    if (!empty($ship_to_city) or !empty($first_array['ship_to_postal_code'])) {
                        $ship_to_postal_code = !empty($first_array['ship_to_postal_code']) ? sprintf($this->language->get('text_tally_format_address_pincode'), $this->limitedHtmlEntitiesConvert($first_array['ship_to_postal_code'])) : '';
                        // if (($transaction_type == 7) or ($transaction_type == 10)) {
                        $customer_shipping_address .= '<ADDRESS>' . $ship_to_city . $ship_to_postal_code . '</ADDRESS>';
                        $customer_address_basic .= '<BASICBUYERADDRESS>' . $ship_to_city . $ship_to_postal_code . '</BASICBUYERADDRESS>';
                        // } elseif ($transaction_type == 8) {
                        //     $customer_shipping_address .= '<BASICBUYERADDRESS>' . $ship_to_city . $ship_to_postal_code . '</BASICBUYERADDRESS>';
                        //     $customer_address_basic .= '<BASICBUYERADDRESS>' . $ship_to_city . $ship_to_postal_code . '</BASICBUYERADDRESS>';
                        // }
                    }

                    if ($first_array['ship_to_country'] != 'IN') {
                        $ship_to_state = !empty($first_array['ship_to_state']) ? $this->limitedHtmlEntitiesConvert($first_array['ship_to_state']) . ', ' : '';
                        if (!empty($ship_to_state)) {
                            // if (($transaction_type == 7) or ($transaction_type == 10)) {
                            $customer_shipping_address .= '<ADDRESS>' . sprintf($this->language->get('text_tally_format_address_state'), $ship_to_state, $country_datas[$first_array['ship_to_country']]) . '</ADDRESS>';
                            $customer_address_basic .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_state'), $ship_to_state, $country_datas[$first_array['ship_to_country']]) . '</BASICBUYERADDRESS>';
                            // } elseif ($transaction_type == 8) {
                            //     $customer_shipping_address .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_state'), $ship_to_state, $country_datas[$first_array['ship_to_country']]) . '</BASICBUYERADDRESS>';
                            //     $customer_address_basic .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_state'), $ship_to_state, $country_datas[$first_array['ship_to_country']]) . '</BASICBUYERADDRESS>';
                            // }
                        }
                    }
                } else {
                    $customer_shipping_address = '';
                    $customer_address_basic = '';
                }

                // Customer Billing Address
                if (isset($first_array['customer_billing_address']) && !$consolidated) {
                    $customer_billing_address = !empty(TRIM($first_array['customer_billing_address'])) ? $this->encrypt->pDecrypt($first_array['customer_billing_address']) : '';
                    if (strlen($customer_billing_address) > 55) {
                        $billing_address_string = wordwrap($customer_billing_address, 55, "~~", false);
                        $billing_address_string = explode('~~', $billing_address_string);

                        $customer_billing_address = '';
                        foreach ($billing_address_string as $address_string) {
                            $address_string = $this->limitedHtmlEntitiesConvert($address_string);
                            if (($transaction_type == 7) or ($transaction_type == 10)) {
                                $customer_billing_address .= '<ADDRESS>' . $address_string . '</ADDRESS>';
                            } elseif ($transaction_type == 8) {
                                $customer_billing_address .= '<BASICBUYERADDRESS>' . $address_string . '</BASICBUYERADDRESS>';
                            }
                        }
                    } elseif (!empty($first_array['customer_billing_address'])) {
                        $customer_billing_address = $this->limitedHtmlEntitiesConvert($customer_billing_address);
                        if (($transaction_type == 7) or ($transaction_type == 10)) {
                            $customer_billing_address = '<ADDRESS>' . $customer_billing_address . '</ADDRESS>';
                        } elseif ($transaction_type == 8) {
                            $customer_billing_address = '<BASICBUYERADDRESS>' . $customer_billing_address . '</BASICBUYERADDRESS>';
                        }
                    } else {
                        $customer_billing_address = '';
                    }

                    $bill_to_city = !empty($first_array['bill_to_city']) ? $this->limitedHtmlEntitiesConvert($first_array['bill_to_city']) . ', ' : '';
                    if (!empty($bill_to_city) or !empty($first_array['bill_to_postal_code'])) {
                        $bill_to_postal_code = !empty($first_array['bill_to_postal_code']) ? sprintf($this->language->get('text_tally_format_address_pincode'), $this->limitedHtmlEntitiesConvert($first_array['bill_to_postal_code'])) : '';
                        if (($transaction_type == 7) or ($transaction_type == 10)) {
                            $customer_billing_address .= '<ADDRESS>' . $bill_to_city . $bill_to_postal_code . '</ADDRESS>';
                        } elseif ($transaction_type == 8) {
                            $customer_billing_address .= '<BASICBUYERADDRESS>' . $bill_to_city . $bill_to_postal_code . '</BASICBUYERADDRESS>';
                        }
                    }

                    if ($first_array['ship_to_country'] != 'IN') { // Shipping address is billing address in GST LAW
                        $bill_to_state = !empty($first_array['bill_to_state']) ? $this->limitedHtmlEntitiesConvert($first_array['bill_to_state']) . ', ' : '';
                        if (!empty($bill_to_state)) {
                            if (($transaction_type == 7) or ($transaction_type == 10)) {
                                $customer_billing_address .= '<ADDRESS>' . sprintf($this->language->get('text_tally_format_address_state_only'), $bill_to_state) . '</ADDRESS>';
                            } elseif ($transaction_type == 8) {
                                $customer_billing_address .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_state_only'), $bill_to_state) . '</BASICBUYERADDRESS>';
                            }
                        }
                    }
                } else {
                    $customer_billing_address = '';
                }

                // Buyer Phone Number
                if (isset($first_array['buyer_phone']) && !empty(trim($first_array['buyer_phone'])) && !$consolidated) {
                    $buyer_phone = $this->encrypt->pDecrypt($first_array['buyer_phone']);
                    // if (($transaction_type == 7) or ($transaction_type == 10)) { // Sales, Debit Note
                    $customer_shipping_address .= '<ADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_phone'), $buyer_phone) . '</ADDRESS>';
                    $customer_billing_address .= '<ADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_phone'), $buyer_phone) . '</ADDRESS>';
                    $customer_address_basic .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_phone'), $buyer_phone) . '</BASICBUYERADDRESS>';
                    // } elseif ($transaction_type == 8) { // Credit Note
                    //     $customer_shipping_address .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_phone'), $buyer_phone) . '</BASICBUYERADDRESS>';
                    //     $customer_billing_address .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_phone'), $buyer_phone) . '</BASICBUYERADDRESS>';
                    //     $customer_address_basic .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_phone'), $buyer_phone) . '</BASICBUYERADDRESS>';
                    // } elseif ($transaction_type == 9) { // Sales Cancel
                    $buyer_phone = '';
                    // }
                } else {
                    $buyer_phone = '';
                }

                // Buyer Email Address
                if (isset($first_array['buyer_email']) && !empty(trim($first_array['buyer_email'])) && !$consolidated) {
                    // if (($transaction_type == 7) or ($transaction_type == 10)) { // Sales, Debit Note
                    $customer_shipping_address .= '<ADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_email'), $first_array['buyer_email']) . '</ADDRESS>';
                    $customer_billing_address .= '<ADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_email'), $first_array['buyer_email']) . '</ADDRESS>';
                    $customer_address_basic .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_email'), $first_array['buyer_email']) . '</BASICBUYERADDRESS>';
                    // } elseif ($transaction_type == 8) { // Credit Note
                    //     $customer_shipping_address .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_email'), $first_array['buyer_email']) . '</BASICBUYERADDRESS>';
                    //     $customer_billing_address .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_email'), $first_array['buyer_email']) . '</BASICBUYERADDRESS>';
                    //     $customer_address_basic .= '<BASICBUYERADDRESS>' . sprintf($this->language->get('text_tally_format_address_buyer_email'), $first_array['buyer_email']) . '</BASICBUYERADDRESS>';
                    // } elseif ($transaction_type == 9) { // Sales Cancel
                    $buyer_email = '';
                    // }
                } else {
                    $buyer_email = '';
                }
                //////// End Here

                $ship_to_country = strtoupper($first_array['ship_to_country']);
                $bill_to_country = strtoupper($first_array['bill_to_country']);
                $is_export_sales = $ship_to_country != 'IN' ? true : false;

                switch ($transaction_type) { // Sales, Debit Note Negative Invoice Total
                    case 7:
                    case 10:
                        $total_invoice_amount = bcmul($total_invoice_amount, -1, 2);
                        break;
                }

                if (
                    !is_null($first_array['einvoice_ack_date']) && $first_array['einvoice_ack_date'] != '0000-00-00'
                    && !empty($first_array['einvoice_ack_date'])
                ) {
                    $einvoice_ack_date = date('Ymd', strtotime($first_array['einvoice_ack_date']));
                } else {
                    $einvoice_ack_date = '';
                }

                // Note: ship_to_city, ship_to_postal_code, bill_to_city, bill_to_postal_code, (buyer_email), (buyer_phone) -> not require now in below array
                $final_datas[] = array(
                    'seller_gstin'              => $seller_gstin,
                    'az_id'                     => $first_array['az_id'],
                    'upload_id'                 => $first_array['upload_id'],
                    'seller_gstin'              => $seller_gstin,
                    'sales_type'                => $sales_type,
                    'sales_type_code'           => $first_array['sales_type'],
                    'state_code'                => isset($first_array['state_code']) ? $first_array['state_code'] : '',
                    'invoice_number'            => isset($first_array['invoice_number']) ? $first_array['invoice_number'] : '',
                    'invoice_date'              => date($this->language->get('datetime_format_tally'), strtotime($first_array['invoice_date'])),
                    'credit_note_no'            => (isset($first_array['credit_note_no']) && $first_array['credit_note_no']) ? $first_array['credit_note_no'] : '',
                    'credit_note_date'          => (isset($first_array['credit_note_date']) && $first_array['credit_note_date'] != '0000-00-00 00:00:00') ? date($this->language->get('datetime_format_tally'), strtotime($first_array['credit_note_date'])) : '',
                    'order_id'                  => isset($first_array['order_id']) ? $first_array['order_id'] : '',
                    'order_date'                => date($this->language->get('datetime_format_tally'), strtotime($first_array['order_date'])),
                    'buyer_gst'                 => isset($first_array['buyer_gst']) ? $first_array['buyer_gst'] : '',
                    'buyer_name'                => htmlentities($first_array['buyer_name']),
                    'bill_type'                 => $bill_type,
                    'tax_rate'                  => isset($tax_rate) ? $tax_rate : '',
                    'taxable_sales'             => $taxable_sales,
                    'original_sale_value'       => isset($original_sale_value) ? $original_sale_value : '',
                    'ord_sale_value'        => isset($ord_sale_value) ? $ord_sale_value : '',  //KM #potter 20250429
                    'nature_of_return'          => isset($nature_of_return) ? $nature_of_return : '',
                    'customer_shipping_name'    => (isset($first_array['customer_shipping_name']) && !$consolidated) ? $this->limitedHtmlEntitiesConvert($this->encrypt->pDecrypt($first_array['customer_shipping_name'])) : '',
                    'customer_shipping_address' => $customer_shipping_address,
                    'customer_address_basic'    => $customer_address_basic,
                    'ship_from_address'         => $first_array['warehouse_address'],
                    'ship_from_city'            => $first_array['warehouse_city'],
                    'ship_from_postal_code'     => $first_array['warehouse_pincode'],
                    'ship_from_state'           => $ship_from_state,
                    'ship_to_city'              => isset($first_array['ship_to_city']) ? $this->limitedHtmlEntitiesConvert($first_array['ship_to_city']) : '',
                    'ship_to_state'             => ($is_export_sales && !isset($first_array['ship_to_state'])) ? '' : ucwords(strtolower($first_array['ship_to_state'])),
                    'ship_to_postal_code'       => isset($first_array['ship_to_postal_code']) ? $first_array['ship_to_postal_code'] : '',
                    'ship_to_country'           => isset($country_datas[$ship_to_country]) ? $country_datas[$ship_to_country] : '',
                    'dispatch_through'          => isset($first_array['dispatch_through']) ? htmlentities($first_array['dispatch_through']) : '',
                    'tracking_number'           => isset($first_array['tracking_number']) ? htmlentities($first_array['tracking_number']) : '',
                    'dispatch_date'             => (!isset($first_array['dispatch_date']) && (empty($first_array['dispatch_date']) or ($first_array['dispatch_date'] == '1970-01-01') or ($first_array['dispatch_date'] == '0000-00-00'))) ? '' : date($this->language->get('datetime_format_tally'), strtotime($first_array['dispatch_date'])),
                    'export_date'               => (!isset($first_array['export_date']) && (empty($first_array['export_date']) or ($first_array['export_date'] == '1970-01-01') or ($first_array['export_date'] == '0000-00-00'))) ? '' : date($this->language->get('datetime_format_tally'), strtotime($first_array['export_date'])),
                    'export_bill_no'            => isset($first_array['export_bill_no']) ? $first_array['export_bill_no'] : '',
                    'export_port_code'          => isset($first_array['export_port_code']) ? $first_array['export_port_code'] : '',
                    'customer_billing_name'     => (isset($first_array['customer_billing_name']) && !$consolidated) ? $this->limitedHtmlEntitiesConvert($this->encrypt->pDecrypt($first_array['customer_billing_name'])) : '',
                    'customer_billing_address'  => $customer_billing_address,
                    'bill_to_city'              => isset($first_array['bill_to_city']) ? $this->limitedHtmlEntitiesConvert($first_array['bill_to_city']) : '',
                    'bill_to_state'             => ($is_export_sales && !isset($first_array['bill_to_state'])) ? '' : $first_array['bill_to_state'],
                    'bill_to_postal_code'       => isset($first_array['bill_to_postal_code']) ? $first_array['bill_to_postal_code'] : '',
                    'bill_to_country'           => isset($country_datas[$bill_to_country]) ? $country_datas[$bill_to_country] : '',
                    'is_export_sales'           => $is_export_sales,
                    'warehouse_id'              => isset($first_array['warehouse_id']) ? htmlentities($first_array['warehouse_id']) : '',
                    'fulfillment_channel'       => isset($first_array['fulfillment_channel']) ? $first_array['fulfillment_channel'] : '',
                    'payment_method_code'       => htmlentities($payment_method_code),
                    'total_invoice_amount'      => number_format((float)$total_invoice_amount, 2, '.', ''),
                    'total_igst_tax'            => $total_interstate_tax <> 0 ? round($total_interstate_tax, 3, PHP_ROUND_HALF_UP) : '',
                    'total_cgst_tax'            => $total_local_tax <> 0 ? bcdiv($total_local_tax, 2, 3) : '',
                    'total_sgst_tax'            => $total_local_tax <> 0 ? bcdiv($total_local_tax, 2, 3) : '',
                    // 'total_utgst_tax'		=> '',
                    'item_promo_discount_basis' => number_format((float)$total_item_promo_discount_basis, 2, '.', ''),
                    'gift_wrap_amount_basis'    => number_format((float)$total_gift_wrap_amount_basis, 2, '.', ''),
                    'gift_wrap_discount_basis'  => number_format((float)$total_gift_wrap_discount_basis, 2, '.', ''),
                    'shipping_amount_basis'     => number_format((float)$total_shipping_amount_basis, 2, '.', ''),
                    'shipping_promo_discount_basis' => number_format((float)$total_shipping_promo_discount_basis, 2, '.', ''),
                    'cod_amount_basis'          => number_format((float)$total_cod_amount_basis, 2, '.', ''),
                    'cod_promo_discount_basis'  => number_format((float)$total_cod_promo_discount_basis, 2, '.', ''),
                    'einvoice_ack_no'           => (!empty($first_array['einvoice_ack_no']) ? $first_array['einvoice_ack_no'] : ''),
                    'einvoice_irn_no'           => (!empty($first_array['einvoice_irn_no']) ? $first_array['einvoice_irn_no'] : ''),
                    'einvoice_ack_date'         => $einvoice_ack_date,
                    'narration'                 => $narration,
                    'inventories'               => $inventories,
                    'excel_inventories'         => $excel_inventories,
                    'tax_summary'               => isset($tax_summary) ? $tax_summary : ''
                );
            }

            if (COUNT($error_data)) { // add error in error sheet
                $comment = sprintf($this->language->get('error_wrong_gst_tax_count'), $first_array['reference'], $first_array['transaction_status'], count($error_data));
                $this->notification->add($comment, $customer_id);
                $this->addUploadError($error_data, $upload_id);
            }
            // $this->log->write($final_datas);
            return $final_datas;
        } else {
            return false;
        }
    }

    protected function getPaymentMethod()
    {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "az_payment_method");
        $result = $query->rows;
        if (count($result)) {
            return $result;
        } else {
            return array();
        }
    }

    protected function updateCancellationData($upload_id, $cancel)
    {
        $this->bug->write('START updateCancellationData');
        $transaction_type = $this->getVoucherNumbers($upload_id);

        if ($transaction_type) {
            $this->load->language('auto/cron');

            if (in_array('9', $transaction_type)) {
                if ($cancel == 5) {
                    // Add deleting data into warning data
                    $query = $this->db->query("SELECT azi.az_id,azi.order_id,azi.invoice_number,azi.invoice_date
                                                    FROM " . DB_PREFIX . "az_invoice azi
                                                    JOIN " . DB_PREFIX . "az_invoice cn ON cn.upload_id = " . (int)$upload_id . " AND cn.order_id = azi.order_id AND cn.shipment_id = azi.shipment_id AND cn.quantity = azi.quantity AND cn.transaction_type = 9
                                                    WHERE azi.upload_id = " . (int)$upload_id . "
                                                        AND azi.transaction_type = 7 ");
                    if (COUNT($query->rows)) {
                        $warning_data = array();
                        foreach ($query->rows as $sales) {
                            $warning_data[] = '(\'' . (int)$upload_id . '\', \'' . $this->db->escape($sales['order_id']) . '\', \'' . $this->db->escape($sales['invoice_number']) . '\', \'' . $this->db->escape($sales['invoice_date']) . '\', \'' . $this->db->escape($this->language->get('warning_delete_sales')) . '\',NOW())';
                        }

                        if (COUNT($warning_data)) {
                            $this->addUploadWarning($warning_data, $upload_id);
                        }

                        // Delete Sales Invoice
                        $this->db->query("DELETE azi,azb,azs,azt,azg,azo,aztc
                                            FROM " . DB_PREFIX . "az_invoice azi
                                            LEFT OUTER JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id
                                            LEFT OUTER JOIN " . DB_PREFIX . "az_shipping azs ON azs.az_id = azi.az_id
                                            LEFT OUTER JOIN " . DB_PREFIX . "az_taxes azt ON azt.az_id = azi.az_id
                                            LEFT OUTER JOIN " . DB_PREFIX . "az_giftwrap azg ON azg.az_id = azi.az_id
                                            LEFT OUTER JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = azi.az_id
                                            LEFT OUTER JOIN " . DB_PREFIX . "az_tcstax aztc ON aztc.az_id = azi.az_id
                                            WHERE azi.az_id IN(" . implode(',', array_column($query->rows, 'az_id')) . ")
                                                AND azi.upload_id = " . (int)$upload_id . " ");
                    }
                } elseif ($cancel == 4) {
                    // Add deleting data into warning data
                    $query = $this->db->query("SELECT cn.az_id,cn.order_id,cn.invoice_number,cn.invoice_date
                                                    FROM " . DB_PREFIX . "az_invoice azi
                                                    JOIN " . DB_PREFIX . "az_invoice cn ON cn.upload_id = " . (int)$upload_id . " AND cn.order_id = azi.order_id AND cn.shipment_id = azi.shipment_id AND cn.quantity = azi.quantity AND cn.transaction_type = 9
                                                    WHERE azi.upload_id = " . (int)$upload_id . "
                                                        AND azi.transaction_type = 7 ");
                    if (COUNT($query->rows)) {
                        $warning_data = array();
                        foreach ($query->rows as $sales) {
                            $warning_data[] = '(\'' . (int)$upload_id . '\', \'' . $this->db->escape($sales['order_id']) . '\', \'' . $this->db->escape($sales['invoice_number']) . '\', \'' . $this->db->escape($sales['invoice_date']) . '\', \'' . $this->db->escape($this->language->get('warning_delete_cancel')) . '\',NOW())';
                        }

                        if (COUNT($warning_data)) {
                            $this->addUploadWarning($warning_data, $upload_id);
                        }

                        // Delete Cancelled Invoice
                        $this->db->query("DELETE cn,azb,azs,azt,azg,azo,aztc
                                            FROM " . DB_PREFIX . "az_invoice cn
                                            LEFT OUTER JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = cn.az_id
                                            LEFT OUTER JOIN " . DB_PREFIX . "az_shipping azs ON azs.az_id = cn.az_id
                                            LEFT OUTER JOIN " . DB_PREFIX . "az_taxes azt ON azt.az_id = cn.az_id
                                            LEFT OUTER JOIN " . DB_PREFIX . "az_giftwrap azg ON azg.az_id = cn.az_id
                                            LEFT OUTER JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = cn.az_id
                                            LEFT OUTER JOIN " . DB_PREFIX . "az_tcstax aztc ON aztc.az_id = cn.az_id
                                            WHERE cn.az_id IN(" . implode(',', array_column($query->rows, 'az_id')) . ")
                                                AND cn.upload_id = " . (int)$upload_id . " ");
                    }
                }
            }

            // New Query 2020-09-14 22:50
            $sql = "UPDATE " . DB_PREFIX . "az_invoice azc
                    JOIN (
                        SELECT DISTINCT asin, sku
                        FROM " . DB_PREFIX . "az_invoice azs
                        WHERE azs.transaction_type = 7
                            AND azs.upload_id = " . (int)$upload_id . "
                    ) as azs
                    ON azs.asin = azc.asin
                    SET
                        azc.sku = azs.sku
                    WHERE azc.upload_id = " . (int)$upload_id . "
                        AND azc.transaction_type = 9";
            $this->db->query($sql);
            //can be marge both query togather
            // Amazon have no item_description then we need to update item_description if its blank
            // $sql = "SELECT DISTINCT asin, sku
            //             FROM " . DB_PREFIX . "az_invoice
            //             WHERE upload_id = " . (int)$upload_id . "
            //             AND transaction_type = 7
            //             AND asin IN (
            //                 SELECT asin
            //                 FROM " . DB_PREFIX . "az_invoice
            //                 WHERE upload_id = " . (int)$upload_id . "
            //                     AND transaction_type = 9
            //             )";

            // $query = $this->db->query($sql);

            // if (COUNT($query->rows)) {
            //     foreach ($query->rows as $row) {
            //         $this->db->query("UPDATE " . DB_PREFIX . "az_invoice
            //                             SET
            //                                 sku = '" . $this->db->escape($row['sku']) . "'
            //                             WHERE asin = '" . $this->db->escape($row['asin']) . "'
            //                                 AND upload_id = " . (int)$upload_id . "
            //                                 AND transaction_type = 9");
            //     }
            // }
        }
        $this->bug->write('START updateCancellationData');
    }

    protected function addTcsCountingError($upload_id, $channel_id)
    {
        // If marketplace is not e-commerce than skip it
        $query = $this->db->query("SELECT COUNT(*) AS total
            FROM " . DB_PREFIX . "channel
            WHERE is_ecommerce = 0
                AND channel_id = " . (int)$channel_id);
        if ($query->row['total']) {
            return true;
        }

        $this->db->query("INSERT INTO " . DB_PREFIX . "upload_error (upload_id,order_id,invoice_number,invoice_date,error_note,added_date)
                            SELECT azi.upload_id,azi.order_id,azi.invoice_number,azi.invoice_date,'" . $this->language->get('error_wrong_tcs_calculation') . "',NOW()
                                FROM " . DB_PREFIX . "az_invoice azi
                                JOIN " . DB_PREFIX . "az_taxes azt ON azt.az_id = azi.az_id AND azt.tax_exclusive_gross <> 0 AND azt.total_tax_amount <> 0
                                JOIN " . DB_PREFIX . "az_tcstax aztcs ON aztcs.az_id = azi.az_id AND (aztcs.tcs_cgst_amount+aztcs.tcs_sgst_amount+aztcs.tcs_igst_amount) = 0
                                WHERE azi.upload_id = " . (int)$upload_id . "
                                GROUP BY azi.order_id,azi.invoice_number,azi.invoice_date");
    }
    protected function checkWrongGstCalculation($upload_id, $gst_id)
    {
        $user_state = $this->getClusterStateByGstId($gst_id);
        $this->db->query("INSERT INTO " . DB_PREFIX . "upload_error (upload_id,order_id,invoice_number,invoice_date,error_note,added_date)
                            SELECT azi.upload_id,azi.order_id,azi.invoice_number,azi.invoice_date,
                                IF(azb.ship_to_state = '" . $this->db->escape($user_state) . "',
                                    REPLACE('Wrong GST Calculated: IGST Calculated instead of CGST and SGST, Ship to state: %s','%s',azb.ship_to_state),
                                    REPLACE('Wrong GST Calculated: SGST/CGST Calculated instead of IGST, Ship to state: %s','%s',azb.ship_to_state)) AS note
                                ,NOW()
                            FROM " . DB_PREFIX . "az_invoice azi
                            JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id AND azb.ship_to_country = 'IN'
                            JOIN " . DB_PREFIX . "az_taxes azt ON azt.az_id = azi.az_id AND IF(azb.ship_to_state = '" . $this->db->escape($user_state) . "',(azt.igst_tax) <> 0,(azt.sgst_tax+azt.cgst_tax) <> 0)
                            WHERE azi.upload_id = " . (int)$upload_id . "
                            GROUP BY azi.invoice_number,azi.order_id");
    }

    protected function pincodeDataUpdate($upload_id)
    {
        $this->bug->write("START pincodeDataUpdate");
        $query = $this->db->query("SELECT COUNT(*) AS total
                                    FROM " . DB_PREFIX . "az_invoice azi
                                    JOIN " . DB_PREFIX . "az_billing azb ON azi.az_id = azb.az_id AND azb.ship_to_city = ''
                                    WHERE azi.upload_id = " . (int)$upload_id . "");
        if ($query->row['total']) {
            $this->db->query("UPDATE " . DB_PREFIX . "az_invoice azi
                                JOIN " . DB_PREFIX . "az_billing azb ON azi.az_id = azb.az_id AND azb.ship_to_city = ''
                                JOIN " . DB_PREFIX . "pincode_data pd ON pd.pincode = azb.ship_to_postal_code
                                SET
                                    azb.ship_to_city = pd.district
                                WHERE azi.upload_id = " . (int)$upload_id . "");
        }

        $query = $this->db->query("SELECT COUNT(*) AS total
                                    FROM " . DB_PREFIX . "az_invoice azi
                                    JOIN " . DB_PREFIX . "az_billing azb ON azi.az_id = azb.az_id AND azb.bill_to_city = ''
                                    WHERE azi.upload_id = " . (int)$upload_id . "");
        if ($query->row['total']) {
            $this->db->query("UPDATE " . DB_PREFIX . "az_invoice azi
                                JOIN " . DB_PREFIX . "az_billing azb ON azi.az_id = azb.az_id AND azb.bill_to_city = ''
                                JOIN " . DB_PREFIX . "pincode_data pd ON pd.pincode = azb.bill_to_postal_code
                                SET
                                    azb.bill_to_city = pd.district
                                WHERE azi.upload_id = " . (int)$upload_id . "");
        }
        $this->bug->write("END pincodeDataUpdate");
    }

    protected function updateAzCancellationWarehouseDataByUploadId($upload_id)
    {
        // Only fetched Sales Records
        $query = $this->db->query("SELECT azi.invoice_number, azi.transaction_type, azo.warehouse_id, azo.fulfillment_channel
                                        FROM " . DB_PREFIX . "az_invoice azi
                                        LEFT JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = azi.az_id
                                            WHERE azi.upload_id = " . (int)$upload_id . " AND transaction_type IN(7)
                                                GROUP BY azo.warehouse_id, azi.transaction_type
                                                ORDER BY azi.transaction_type ASC");
        $invoice_datas = $query->rows;
        if (count($invoice_datas)) {

            foreach ($invoice_datas as $invoice_data) {
                $invoice_series = explode("-", $invoice_data['invoice_number']);
                $invoice_series = $invoice_series[0]; // Invoice Prefix Like AMD1
                $invoice_series = $invoice_series . '-'; // Dash added to get accurate data

                // Only Cancelled Invoice Warehouse Data update
                $this->db->query("UPDATE " . DB_PREFIX . "az_invoice azi
                                    LEFT JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = azi.az_id
                                        SET
                                            azo.warehouse_id = '" . $this->db->escape($invoice_data['warehouse_id']) . "',
                                            azo.fulfillment_channel = '" . $this->db->escape($invoice_data['fulfillment_channel']) . "'
                                                WHERE azi.upload_id = " . (int)$upload_id . "
                                                    AND azi.invoice_number LIKE '" . $this->db->escape($invoice_series) . "%'
                                                    AND azi.transaction_type = 9");
            }
        } else { // No action needed
            return false;
        }
    }
    protected function checkDuplicateCancelledOrders($upload_id, $upload_data)
    {
        // Date 26-09-2020 this feature is not working due to less data maintained in DB
        // Need to verify this calculation and database and make it active asap.
        // Pending
        return 1;
        if ($this->checkMwsSetting($upload_data['channel_id'], $upload_data['usr'])) { // Check if user have mws_setting then run below function
            $sql = "SELECT azi.az_id,azi.order_id,azi.asin,azi.invoice_number,DATE_FORMAT(azi.order_date, '%Y-%m-%d') as order_date,azi.invoice_date
                        FROM " . DB_PREFIX . "az_invoice azi
                        JOIN " . DB_PREFIX . "az_invoice cn ON cn.upload_id = " . (int)$upload_id . " AND cn.order_id = azi.order_id AND cn.shipment_id = azi.shipment_id AND cn.quantity <> azi.quantity AND cn.transaction_type = 9
                        WHERE azi.upload_id = " . (int)$upload_id . "
                            AND azi.transaction_type = 7";
            $query = $this->db->query($sql);
            $error_orders = $query->rows;
            foreach ($error_orders as $error_order) {
                $query = $this->db->query("SELECT mo.order_id,moi.order_item_id,moi.asin,moi.seller_sku,moi.order_quantity,moi.shipping_amount,moi.shipping_tax_amount,moi.shipping_discount_amount,moi.shipping_discount_amount_tax,moi.gift_amount,moi.gift_tax_amount,moi.promotion_discount,moi.promotion_discount_tax,moi.product_total_amount,moi.product_tax_amount
                                            FROM " . DB_PREFIX . "mws_order mo
                                            JOIN " . DB_PREFIX . "mws_batch b ON b.batch_id = mo.batch_id AND b.customer_id = " . (int)$upload_data['usr'] . "
                                            JOIN " . DB_PREFIX . "remarketing_api ra ON ra.api_id = b.api_id AND ra.channel = " . (int)$upload_data['channel_id'] . "
                                            JOIN " . DB_PREFIX . "mws_order_item moi ON moi.mws_order_id = mo.mws_order_id AND moi.asin = '" . $this->db->escape($error_order['asin']) . "'
                                            WHERE mo.order_id = '" . $this->db->escape($error_order['order_id']) . "'
                                                AND mo.customer_id = " . (int)$upload_data['usr'] . "
                                                AND LOWER(mo.order_status) <> 'canceled'
                                                AND DATE(mo.order_date) = DATE('" . $this->db->escape($error_order['order_date']) . "')");
                $result = $query->rows;
                if (COUNT($result)) { // May chance to get multiple items in single order
                    foreach ($result as $res) {
                        // Only Sales transaction will update
                        $query = $this->db->query("SELECT azi.az_id,igst_rate,cgst_rate,sgst_rate,(igst_rate + cgst_rate + sgst_rate) AS tax_rate,aztcs.tcs_cgst_rate,aztcs.tcs_sgst_rate,aztcs.tcs_igst_rate
                                                    FROM " . DB_PREFIX . "az_invoice azi
                                                    JOIN " . DB_PREFIX . "az_taxes azt ON azt.az_id = azi.az_id
                                                    JOIN " . DB_PREFIX . "az_tcstax aztcs ON aztcs.az_id = azi.az_id
                                                    WHERE azi.asin = '" . $this->db->escape($res['asin']) . "'
                                                        AND azi.quantity <> " . (int)$res['order_quantity'] . "
                                                        AND azi.order_id = '" . $this->db->escape($res['order_id']) . "'
                                                        AND azi.transaction_type = 7
                                                        AND azi.upload_id = " . (int)$upload_id . " ");
                        if ($az_result = $query->row) {
                            $az_id = $az_result['az_id'];
                            $tax_rate = $az_result['tax_rate'];
                            $tcs_rate = array_sum(array($az_result['tcs_igst_rate'], $az_result['tcs_cgst_rate'], $az_result['tcs_sgst_rate']));

                            // if product_tax_amount exisit then take according else calculate
                            $invoice_amount = array_sum(array($res['shipping_amount'], $res['shipping_tax_amount'], $res['shipping_discount_amount'], $res['shipping_discount_amount_tax'], $res['gift_amount'], $res['gift_tax_amount'], $res['promotion_discount'], $res['promotion_discount_tax'], $res['product_total_amount'], $res['product_tax_amount']));
                            if ($tax_rate) { // Taxable Product
                                $shipping_amount_basis = ($res['shipping_tax_amount'] != 0) ? $res['shipping_amount'] : bcdiv($res['shipping_amount'], bcadd(1, bcdiv($tax_rate, 100, 2), 2), 2);
                                $shipping_promo_discount_basis = ($res['shipping_discount_amount_tax'] != 0) ? $res['shipping_discount_amount'] : bcdiv($res['shipping_discount_amount'], bcadd(1, bcdiv($tax_rate, 100, 2), 2), 2);
                                $gift_wrap_amount_basis = ($res['gift_tax_amount'] != 0) ? $res['gift_amount'] : bcdiv($res['gift_amount'], bcadd(1, bcdiv($tax_rate, 100, 2), 2), 2);
                                $item_promo_discount_basis = ($res['promotion_discount_tax'] != 0) ? $res['promotion_discount'] : bcdiv($res['promotion_discount'], bcadd(1, bcdiv($tax_rate, 100, 2), 2), 2);
                                $principal_amount = ($res['product_tax_amount'] != 0) ? bcadd($res['product_total_amount'], $res['product_tax_amount'], 2) : $res['product_total_amount'];
                                $principal_amount_basis = ($res['product_tax_amount'] != 0) ? $res['product_total_amount'] : bcdiv($res['product_total_amount'], bcadd(1, bcdiv($tax_rate, 100, 2), 2), 2);
                                $tax_exclusive_gross = bcdiv($invoice_amount, bcadd(1, bcdiv($tax_rate, 100, 2), 2), 2);
                            } else { // Nil-rated product
                                $shipping_amount_basis = $res['shipping_amount'];
                                $shipping_promo_discount_basis = $res['shipping_discount_amount'];
                                $gift_wrap_amount_basis = $res['gift_amount'];
                                $item_promo_discount_basis = $res['promotion_discount'];
                                $principal_amount = $res['product_total_amount'];
                                $principal_amount_basis = $res['product_total_amount'];
                                $tax_exclusive_gross = $invoice_amount;
                            }

                            $total_tax_amount = bcsub($invoice_amount, $tax_exclusive_gross, 2);
                            $total_tcs_amount = bcdiv(bcmul($tax_exclusive_gross, $tcs_rate, 2), 100, 2);

                            $final_array = array(
                                'invoice_amount'                => $invoice_amount,
                                'quantity'                      => $res['order_quantity'],
                                'gift_wrap_amount_basis'        => $gift_wrap_amount_basis,
                                'shipping_amount_basis'         => $shipping_amount_basis,
                                'shipping_promo_discount_basis' => $shipping_promo_discount_basis,
                                'tax_exclusive_gross'           => $tax_exclusive_gross,
                                'total_tax_amount'              => $total_tax_amount,
                                'cgst_tax'                      => ($az_result['cgst_rate'] != 0) ? bcdiv($total_tax_amount, 2, 2) : 0,
                                'sgst_tax'                      => ($az_result['sgst_rate'] != 0) ? bcdiv($total_tax_amount, 2, 2) : 0,
                                'igst_tax'                      => ($az_result['igst_rate'] != 0) ? $total_tax_amount : 0,
                                'principal_amount'              => $principal_amount,
                                'principal_amount_basis'        => $principal_amount_basis,
                                'item_promo_discount_basis'     => $item_promo_discount_basis,
                                'tcs_cgst_amount'               => ($az_result['tcs_cgst_rate'] != 0) ? bcdiv($total_tcs_amount) : 0,
                                'tcs_sgst_amount'               => ($az_result['tcs_sgst_rate'] != 0) ? bcdiv($total_tcs_amount) : 0,
                                'tcs_igst_amount'               => ($az_result['tcs_igst_rate'] != 0) ? $total_tcs_amount : 0
                            );
                            $this->bug->write("ALERT auto/cron/checkDuplicateCancelledOrders final_array: " . json_encode($final_array));

                            $this->db->query("UPDATE " . DB_PREFIX . "az_invoice azi
                                                JOIN " . DB_PREFIX . "az_giftwrap azg ON azg.az_id = azi.az_id
                                                JOIN " . DB_PREFIX . "az_shipping azs ON azs.az_id = azi.az_id
                                                JOIN " . DB_PREFIX . "az_taxes azt ON azt.az_id = azi.az_id
                                                JOIN " . DB_PREFIX . "az_tcstax aztcs ON aztcs.az_id = azi.az_id
                                                SET
                                                    azi.invoice_amount = " . (float)$final_array['invoice_amount'] . ",
                                                    azi.quantity = " . (float)$final_array['quantity'] . ",
                                                    azg.gift_wrap_amount_basis = " . (float)$final_array['gift_wrap_amount_basis'] . ",
                                                    azs.shipping_amount_basis = " . (float)$final_array['shipping_amount_basis'] . ",
                                                    azs.shipping_promo_discount_basis = " . (float)$final_array['shipping_promo_discount_basis'] . ",
                                                    azt.tax_exclusive_gross = " . (float)$final_array['tax_exclusive_gross'] . ",
                                                    azt.total_tax_amount = " . (float)$final_array['total_tax_amount'] . ",
                                                    azt.cgst_tax = " . (float)$final_array['cgst_tax'] . ",
                                                    azt.sgst_tax = " . (float)$final_array['sgst_tax'] . ",
                                                    azt.igst_tax = " . (float)$final_array['igst_tax'] . ",
                                                    azt.principal_amount = " . (float)$final_array['principal_amount'] . ",
                                                    azt.principal_amount_basis = " . (float)$final_array['principal_amount_basis'] . ",
                                                    azt.item_promo_discount_basis = " . (float)$final_array['item_promo_discount_basis'] . ",
                                                    aztcs.tcs_cgst_amount = " . (float)$final_array['tcs_cgst_amount'] . ",
                                                    aztcs.tcs_sgst_amount = " . (float)$final_array['tcs_sgst_amount'] . ",
                                                    aztcs.tcs_igst_amount = " . (float)$final_array['tcs_igst_amount'] . "
                                                WHERE azi.az_id = " . (int)$az_id . " ");
                        }

                        // Delete cancelled transaction
                        $this->db->query("DELETE azi,azb,azg,azo,azs,azt,aztcs,azin
                                            FROM " . DB_PREFIX . "az_invoice azi
                                            JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id
                                            JOIN " . DB_PREFIX . "az_giftwrap azg ON azg.az_id = azi.az_id
                                            JOIN " . DB_PREFIX . "az_other azo ON azo.az_id = azi.az_id
                                            JOIN " . DB_PREFIX . "az_shipping azs ON azs.az_id = azi.az_id
                                            JOIN " . DB_PREFIX . "az_taxes azt ON azt.az_id = azi.az_id
                                            JOIN " . DB_PREFIX . "az_tcstax aztcs ON aztcs.az_id = azi.az_id
                                            LEFT OUTER JOIN " . DB_PREFIX . "az_invoice_note azin ON azin.az_id = azi.az_id
                                            WHERE azi.asin = '" . $this->db->escape($res['asin']) . "'
                                                AND azi.order_id = '" . $this->db->escape($res['order_id']) . "'
                                                AND azi.transaction_type = 9
                                                AND azi.upload_id = " . (int)$upload_id . "");

                        // Notification added
                        $comment = sprintf($this->language->get('notification_cancelled_order_removed'), $res['order_id'], $upload_data['reference']);
                        $this->notification->add($comment, $upload_data['usr']);

                        // Error added into batch error log
                        $error_data = array();
                        $error_data[] = array('order_id' => $res['order_id'], 'invoice_number' => $error_order['invoice_number'], 'invoice_date' => $error_order['invoice_date'], 'error_note' => $comment, 'error_code' => 420);
                        $this->addUploadError($error_data, $upload_id);
                    }
                }
            }
        }
    }
    protected function checkMwsSetting($channel_id, $customer_id)
    {
        $query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "mws_setting ms
                                    LEFT OUTER JOIN " . DB_PREFIX . "remarketing_api ra ON ra.api_id = ms.api_id AND ra.channel = " . (int)$channel_id . "
                                        WHERE ms.customer_id = " . (int)$customer_id . " AND ra.channel = " . (int)$channel_id . "");
        $result = $query->row['total'];
        if ($result) {
            return true;
        } else {
            return false;
        }
    }

    public function getChargesByPlanId($plan_code)
    {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "charges WHERE plan_code = " . (int)$plan_code . " ");

        if (count($query->rows)) {
            $charges = array();
            foreach ($query->rows as $data) {
                $charges[$data['status_type']] = $data['charges'];
            }
            return $charges;
        } else {
            return false;
        }
    }

    public function emailInsufficientBalance($charged_amount, $available_balance, $difference_amount, $customer_id)
    {
        $this->load->model('billing/customer');
        $this->load->model('api/request');

        $customer_info = $this->getCustomer($customer_id);

        $email_data = array(
            'website_name'      => $this->config->get('config_name'),
            'website_url'       => $this->config->get('config_url'),
            'full_name'         => $customer_info['fullname'],
            'gst_number'        => strtoupper($customer_info['gst_number']),
            'amount'            => $this->currency->format($charged_amount, 'Ptn'),
            'balance'           => $this->currency->format($available_balance, 'Ptn'),
            'difference_amount' => $this->currency->format($difference_amount, 'Ptn'),
            'to_email'          => $customer_info['email']
        );

        $this->model_api_request->sendEmail($email_data, 'insufficient_balance');
    }

    public function updateConsolidatedByUploadId($upload_id, $is_consolidated_value)
    {
        $this->db->query("UPDATE " . DB_PREFIX . "upload SET is_consolidated = " . (int)$is_consolidated_value . " WHERE upload_id = " . (int)$upload_id . "");
    }

    public function updateCharges($charged_amount, $upload_id)
    {
        $this->db->query("UPDATE " . DB_PREFIX . "upload SET
                                charge = '" . $this->db->escape($charged_amount) . "'
                                WHERE upload_id = " . (int)$upload_id . "");
    }

    /**
     * Update State Name By Pincode DB
     * System can check pincode is exisit in pincode database and get state name and
     * updated into az_billing's ship_to_state and bill_to_state field
     */
    public function updateStateNameByUploadId($upload_id)
    { // Updated On 13-09-2020
        $this->bug->write("START updateStateNameByUploadId");
        // This function will correct only INDIAN state name where state name is not mention in our state data.
        // This data will be updated using pincode

        // Update azb.ship_to_state
        $this->db->query("UPDATE " . DB_PREFIX . "az_invoice azi
                            JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id AND azb.ship_to_postal_code <> ''
                                                        AND azb.ship_to_country = 'IN'
                                                        AND NOT EXISTS(
                                                            SELECT
                                                                s.name
                                                            FROM " . DB_PREFIX . "pincode_data pd
                                                            JOIN " . DB_PREFIX . "state s ON s.id = pd.state_id
                                                            WHERE s.name = azb.ship_to_state
                                                        )
                            JOIN " . DB_PREFIX . "pincode_data pd ON pd.pincode = azb.ship_to_postal_code
                            JOIN " . DB_PREFIX . "state s ON s.id = pd.state_id
                            SET
                                azb.ship_to_state =
                                    IF(
                                        s.applicable_till IS NOT NULL,
                                        (CASE
                                            WHEN DATE_FORMAT(azi.invoice_date, '%Y-%m-%d') <= s.applicable_till THEN s.old_name
                                            WHEN s.name = '' THEN azb.ship_to_state
                                            ELSE s.name
                                        END),
                                        s.name
                                    )
                            WHERE azi.upload_id = " . (int)$upload_id . "");

        // Update azb.bill_to_state
        $this->db->query("UPDATE " . DB_PREFIX . "az_invoice azi
                            JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id AND azb.bill_to_postal_code <> ''
                                                        AND azb.ship_to_country = 'IN'
                                                        AND NOT EXISTS(
                                                            SELECT
                                                                s.name
                                                            FROM " . DB_PREFIX . "pincode_data pd
                                                            JOIN " . DB_PREFIX . "state s ON s.id = pd.state_id
                                                            WHERE s.name = azb.bill_to_state
                                                        )
                            JOIN " . DB_PREFIX . "pincode_data pd ON pd.pincode = azb.bill_to_postal_code
                            JOIN " . DB_PREFIX . "state s ON s.id = pd.state_id
                            SET
                                azb.bill_to_state =
                                    IF(
                                        s.applicable_till IS NOT NULL,
                                        (CASE
                                            WHEN DATE_FORMAT(azi.invoice_date, '%Y-%m-%d') <= s.applicable_till THEN s.old_name
                                            WHEN s.name = '' THEN azb.bill_to_state
                                            ELSE s.name
                                        END),
                                        s.name
                                    )
                            WHERE azi.upload_id = " . (int)$upload_id . "");

        // Need to pass applicable_till in below condition
        // Value not updated for old dates
        // Due to wrong pincode state name not able to update.
        // Ship to State
        $this->db->query("INSERT INTO " . DB_PREFIX . "state_unmapped (upload_id,state_name)
                            SELECT " . (int)$upload_id . ", azb.ship_to_state
                            FROM " . DB_PREFIX . "az_invoice azi
                            JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id AND azb.ship_to_country = 'IN'
                            WHERE azi.upload_id = " . (int)$upload_id . "
                                AND NOT EXISTS (
                                    SELECT
                                        IF(
                                            s.applicable_till IS NOT NULL,
                                            (CASE
                                                WHEN DATE_FORMAT(azi.invoice_date, '%Y-%m-%d') <= s.applicable_till THEN s.old_name
                                                ELSE s.name
                                            END),
                                            s.name
                                        ) as name
                                    FROM " . DB_PREFIX . "state s
                                    WHERE azb.ship_to_state = (
                                        IF(
                                            s.applicable_till IS NOT NULL,
                                            (CASE
                                                WHEN DATE_FORMAT(azi.invoice_date, '%Y-%m-%d') <= s.applicable_till THEN s.old_name
                                                ELSE s.name
                                            END),
                                            s.name
                                        )
                                    )
                                )
                                AND NOT EXISTS(
                                    SELECT su.state_name
                                    FROM " . DB_PREFIX . "state_unmapped su
                                    WHERE su.state_name = azb.ship_to_state
                                        AND su.upload_id = " . (int)$upload_id . "
                                )
                            GROUP BY azb.ship_to_state");
        // Bill to State
        $this->db->query("INSERT INTO " . DB_PREFIX . "state_unmapped (upload_id,state_name)
                            SELECT " . (int)$upload_id . ", azb.bill_to_state
                            FROM " . DB_PREFIX . "az_invoice azi
                            JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id AND azb.ship_to_country = 'IN'
                            WHERE azi.upload_id = " . (int)$upload_id . "
                                AND NOT EXISTS(
                                    SELECT
                                        IF(
                                            s.applicable_till IS NOT NULL,
                                            (CASE
                                                WHEN DATE_FORMAT(azi.invoice_date, '%Y-%m-%d') <= s.applicable_till THEN s.old_name
                                                ELSE s.name
                                            END),
                                            s.name
                                        ) as name
                                    FROM " . DB_PREFIX . "state s
                                    WHERE azb.bill_to_state = (
                                        IF(
                                            s.applicable_till IS NOT NULL,
                                            (CASE
                                                WHEN DATE_FORMAT(azi.invoice_date, '%Y-%m-%d') <= s.applicable_till THEN s.old_name
                                                ELSE s.name
                                            END),
                                            s.name
                                        )
                                    )
                                )
                                AND NOT EXISTS(
                                    SELECT su.state_name
                                    FROM " . DB_PREFIX . "state_unmapped su
                                    WHERE su.state_name = azb.bill_to_state
                                        AND su.upload_id = " . (int)$upload_id . "
                                )
                            GROUP BY azb.bill_to_state");

        // Update azb.ship_from_state & azb.bill_from_state if require
        //

        $this->bug->write("END updateStateNameByUploadId");
    }

    public function getUnmappedState($upload_id)
    {
        $query = $this->db->query("SELECT COUNT(*) AS total
                                    FROM " . DB_PREFIX . "state_unmapped
                                    WHERE upload_id = " . (int)$upload_id . "");
        return $query->row['total'];
    }

    public function getChannelName($channel_id)
    {
        $query = $this->db->query("SELECT UPPER(name) AS channel_name
                                    FROM " . DB_PREFIX . "channel
                                    WHERE channel_id = " . (int)$channel_id . "");
        if ($query->row) {
            return $query->row['channel_name'];
        } else {
            return false;
        }
    }

    public function getMonthYear($upload_id)
    {
        $query = $this->db->query("SELECT DATE_FORMAT(invoice_date, '%m%Y') AS month_year
                                    FROM " . DB_PREFIX . "az_invoice
                                    WHERE upload_id = " . (int)$upload_id . "
                                    LIMIT 1");
        if ($query->row) {
            return $query->row['month_year'];
        } else {
            return false;
        }
    }

    public function getClusterStateByGstId($gst_id)
    {
        $query = $this->db->query("SELECT state AS state_name FROM " . DB_PREFIX . "gst WHERE gst_id = " . (int)$gst_id . "");
        if ($query->row) {
            return strtolower($query->row['state_name']);
        } else {
            return false;
        }
    }

    protected function getUpload($upload_id)
    {
        $query = $this->db->query("SELECT u.reference,u.note,CONCAT(c.firstname,' ', c.lastname) AS full_name,c.email, cc.gst_number AS gst_number,g.gst_number AS batch_gst_number FROM " . DB_PREFIX .  "upload u
                                        LEFT JOIN " . DB_PREFIX . "gst g ON g.gst_id = u.gst_id
                                        LEFT JOIN " . DB_PREFIX . "customer c ON c.customer_id = u.usr
                                        LEFT JOIN " . DB_PREFIX . "customer_company cc ON cc.customer_id = c.customer_id
                                            WHERE u.upload_id = " . (int)$upload_id . " ");
        if ($query->row) {
            return $query->row;
        } else {
            return false;
        }
    }

    protected function updateAzCustomerInfoByUploadId($channel_id, $order_ids, $customer_id)
    {
        if (COUNT($order_ids)) {
            $order_id_string = implode(',', $order_ids);
            $query = $this->db->query("SELECT m.order_id,m.shipping_name,m.shipping_address,m.buyer_email,m.phone
                                        FROM " . DB_PREFIX . "mws_order m
                                        JOIN " . DB_PREFIX . "mws_batch mb ON mb.batch_id = m.batch_id AND mb.customer_id = m.customer_id
                                        JOIN " . DB_PREFIX . "remarketing_api ra ON ra.api_id = mb.api_id AND ra.channel = " . (int)$channel_id . "
                                        WHERE m.customer_id = " . (int)$customer_id . "
                                            AND LOWER(m.order_status) <> 'cancelled'
                                            AND m.buyer_name <> ''
                                            AND FIND_IN_SET(m.order_id, '" . $this->db->escape($order_id_string) . "')
                                        GROUP BY m.order_id");
            $result = $query->rows;
            if (COUNT($result)) {
                $final_array = array();
                foreach ($result as $res) {
                    $final_array[$res['order_id']] = array(
                        'shipping_name'        => $res['shipping_name'],
                        'shipping_address'    => $res['shipping_address'],
                        'buyer_email'        => $res['buyer_email'],
                        'phone'                => $res['phone']
                    );
                }

                return $final_array;
            } else {
                return array();
            }
        }
    }

    protected function updateAzCityInfoByUploadId($upload_id, $channel_id, $customer_id)
    { // Function for addCsvDatas -> to add bill_to_city and ship_to_city names
        $query = $this->db->query("SELECT DISTINCT ship_to_postal_code, bill_to_postal_code FROM " . DB_PREFIX . "az_invoice azi
                                        LEFT JOIN " . DB_PREFIX . "az_billing azb ON azb.az_id = azi.az_id
                                            WHERE azi.upload_id = " . (int)$upload_id . " ");
        $query = $query->rows;

        if (COUNT($query)) {
            $ship_to_pincode_array = array_unique(array_column($query, 'ship_to_postal_code'));
            $bill_to_pincode_array = array_unique(array_column($query, 'bill_to_postal_code'));
            $pincode_data_array = array_unique(array_merge($ship_to_pincode_array, $bill_to_pincode_array));

            foreach ($pincode_data_array as $pincode) {
                $sql = "UPDATE " . DB_PREFIX . "az_billing azb
                            RIGHT JOIN " . DB_PREFIX . "az_invoice azi ON azi.az_id = azb.az_id AND azi.upload_id = " . (int)$upload_id . "
                            RIGHT JOIN " . DB_PREFIX . "pincode_data pd ON pd.pincode = '" . $this->db->escape($pincode) . "'
                                SET
                                    azb.ship_to_city =
                                        (CASE
                                            WHEN azb.ship_to_postal_code = '" . $this->db->escape($pincode) . "' THEN pd.district
                                            ELSE azb.ship_to_city
                                        END),
                                    azb.bill_to_city =
                                        (CASE
                                            WHEN azb.bill_to_postal_code = '" . $this->db->escape($pincode) . "' THEN pd.district
                                            ELSE azb.bill_to_city
                                        END)
                                WHERE azi.upload_id = " . (int)$upload_id . "
                                    AND (azb.ship_to_postal_code = '" . $this->db->escape($pincode) . "' OR azb.bill_to_postal_code = '" . $this->db->escape($pincode) . "')";
                $query = $this->db->query($sql);
            }
        }
    }

    public function sendInsufficientData($data_health, $upload_id)
    {
        $this->load->model('api/request');
        $this->load->language('auto/cron');

        $insufficient_data = array();
        foreach ($data_health as $key => $data) {
            $insufficient_data[] = sprintf($this->language->get('text_insufficient_data_' . $key), $data);
        }

        if (count($insufficient_data)) {
            $insufficient_data_string = implode("", $insufficient_data);
        } else {
            $insufficient_data_string = '';
        }

        $upload_info = $this->getUpload($upload_id);

        $email_data = array(
            'website_name'         => $this->config->get('config_name'),
            'website_url'          => $this->config->get('config_url'),
            'full_name'           => $upload_info['full_name'],
            'gst_number'           => strtoupper($upload_info['gst_number']),
            'batch_gst_number'     => strtoupper($upload_info['batch_gst_number']),
            'batch_reference'   => $upload_info['reference'],
            'batch_note'        => $upload_info['note'],
            'insufficient_data'    => $insufficient_data_string,
            'to_email'             => $upload_info['email']
        );

        $this->model_api_request->sendEmail($email_data, 'insufficient_data');
    }

    // To get country data
    public function get2DigitCountryCode()
    {
        $query = $this->db->query("SELECT UPPER(code) AS code,LOWER(name) AS name FROM " . DB_PREFIX . "country ");
        $result = $query->rows;
        if (count($result)) {
            return array_column($result, 'code', 'name');
        } else {
            return array();
        }
    }

    // To get state data
    public function get2DigitStateCode()
    {
        $query = $this->db->query("SELECT UPPER(short_code) AS code,LOWER(name) AS name FROM " . DB_PREFIX . "state ");
        if (count($query->rows)) {
            return array_column($query->rows, 'code', 'name');
        } else {
            return array();
        }
    }

    // To get state name using numeric tincode
    public function getTinCodeStateCode()
    {
        $query = $this->db->query("SELECT tin_code,name FROM " . DB_PREFIX . "state ");
        if (count($query->rows)) {
            $final_array = array();
            foreach ($query->rows as $row) {
                $final_array[(int)$row['tin_code']] = $row['name'];
            }
            return $final_array;
        } else {
            return array();
        }
    }

    public function getAllEbayProductCodes($customer_id, $channel_id)
    {
        // Channel_id, Customer_id wise unique data
        $query = $this->db->query("SELECT
                                        product_sku, product_title
                                    FROM " . DB_PREFIX . "product_ebay_temp_sku
                                    WHERE channel_id = " . (int)$channel_id . "
                                        AND customer_id = " . (int)$customer_id . "");
        $query = $query->rows;

        if (COUNT($query)) {
            $datas = array();
            foreach ($query as $q) {
                $datas[$q['product_sku']] = $q['product_title'];
            }
            return $datas;
        } else {
            return array();
        }
    }

    protected function getEbayProductShortCode($string, $code_length)
    {
        if (strpos($string, ' ') !== false) {
            preg_match_all('/(?<=\s|^)[a-z]/i', $string, $matches);
            $matches = array_shift($matches);
            $code = '';

            if (COUNT($matches)) {
                if (strtolower($matches[0]) == 'x') {
                    unset($matches[0]);
                }
            }

            $count = 0;
            foreach ($matches as $key => $match) {
                $code .= $match;
                $count += 1;
                if ($code_length == $count) {
                    break;
                }
            }

            $short_code = strtoupper($code);

            return $short_code;
        } else {
            $code = substr($string, 0, $code_length);
            $short_code = strtoupper($code);

            return $short_code;
        }
    }

    protected function getOrderCount($upload_id)
    {
        $query = $this->db->query("SELECT COUNT(*) AS total
                                    FROM " . DB_PREFIX . "az_invoice
                                    WHERE upload_id = " . (int)$upload_id . "");
        return $query->row['total'];
    }

    // Dynamic SKU Start
    protected function getEbayProductCode($product_title, $customer_id, $channel_id)
    {
        // channel_id, customer_id wise unique data
        $query = $this->db->query("SELECT
                                        product_sku
                                    FROM " . DB_PREFIX . "product_ebay_temp_sku
                                    WHERE product_title = '" . $this->db->escape($product_title) . "'
                                        AND channel_id = " . (int)$channel_id . "
                                        AND customer_id = " . (int)$customer_id . " ");

        if ($query->row) {
            return $query->row['product_sku'];
        } else {
            return false;
        }
    }

    protected function checkEbayProductCode($unique_token, $customer_id, $channel_id)
    {
        // Channel_id, Customer_id wise unique data
        $query = $this->db->query("SELECT
                                        COUNT(*) AS total
                                    FROM " . DB_PREFIX . "product_ebay_temp_sku
                                    WHERE product_sku = '" . $this->db->escape($unique_token) . "'
                                        AND channel_id = " . (int)$channel_id . "
                                        AND customer_id = " . (int)$customer_id . "");

        return $query->row['total'];
    }

    public function createEbayProductCode($product_title, $company_name, $customer_id, $channel_id)
    {
        $this->load->model('system/other');

        // We can skip this step because its not called, we added it in array in auto/cron to save time
        if ($product_sku = $this->getEbayProductCode($product_title, $customer_id, $channel_id)) {
            $this->bug->write("DANGER auto/cron -> createEbayProductCode: Product Title: " . $product_title . ' || Customer ID: ' . $customer_id);
            return $product_sku;
        } else {
            $company_name_code = $this->getEbayProductShortCode($company_name, 2);
            $product_title_code = $this->getEbayProductShortCode($product_title, 2);
            $prefix = $company_name_code . '-' . $product_title_code . '-';
            $length = 10;

            // Creating Dynamic SKU
            $count = 0;
            while ($count < 2) {
                $unique_token = $this->model_system_other->generateUniqueToken($prefix, $length);

                if (!$this->checkEbayProductCode($unique_token, $customer_id, $channel_id)) {
                    $product_sku = $unique_token;
                    $count++;
                }
            }

            // Insert Dynamic Sku into ebay_temp_sku table
            if (!empty($product_sku)) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_ebay_temp_sku
                                    SET
                                        channel_id = " . (int)$channel_id . ",
                                        product_sku = '" . $this->db->escape($product_sku) . "',
                                        product_title = '" . $this->db->escape($product_title) . "',
                                        added_date = NOW(),
                                        customer_id = " . (int)$customer_id . "");

                if ($this->db->getLastId()) {
                    return $product_sku;
                } else {
                    return false;
                }
            } else {
                $this->bug->write("DANGER model/auto/cron -> createEbayProductCode: getting blank product_sku Datas-> Product Title: " . $product_title . " || Company Name: " . $company_name . " || Customer Id: " . $customer_id . " || Channel Id: " . $channel_id);
            }
        }
    }
    // Dynamic SKU End

    protected function limitedHtmlEntitiesConvert($string)
    {
        if (!empty($string)) {
            $string = html_entity_decode($string);

            $search  = array('&', '<', '>', '"', '\'');
            $replace = array('&amp;', '&lt;', '&gt;', '&quot;', '&apos;');
            $final_string = str_replace($search, $replace, $string);

            return $final_string;
        } else {
            return $string;
        }
    }

    //#KM potter ********
    protected function thoroughlyCleanString($string)
    {
        if (empty($string)) {
            return $string;
        }

        // First convert to HTML entities and then decode to handle any existing entities
        $string = html_entity_decode(htmlentities($string), ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Remove any remaining &nbs variations with regex
        $string = preg_replace('/&n(?:bs)?p?;?/i', '', $string);

        // Final cleanup of any remaining problematic sequences
        $string = str_replace(['&nbs', '&nbsp', '&nb', '&n'], '', $string);

        // Trim any whitespace
        $string = trim($string);

        return $string;
    }
    //#KM potter ********

    /**
     * Get Unique Reference Number
     */
    protected function getUniqueCode()
    {
        $this->load->model('account/account');

        $count = 0;
        while ($count < 2) {
            $unique_code = strtoupper(uniqid(PREFIX_UPLOAD_BATCH));

            if (!$this->model_account_account->checkUniqueUploadCode($unique_code)) {
                return $unique_code;
                $count++;
            }
        }
    }

    /**
     * Get GST Rate
     *
     * Calulate GST Rate according gst amount and taxable amount
     *
     * @param float $gst_amount
     * @param float $taxable_item_amount
     * @return float
     */
    protected function getGstRate($gst_amount, $taxable_item_amount)
    {
        $gst_amount = abs($gst_amount);
        $taxable_item_amount = abs($taxable_item_amount);

        foreach ($this->gst_rates as $gst_rate) {
            $calculated_gst_amount = bcsub(bcmul($taxable_item_amount, bcadd(1, bcdiv($gst_rate, 100, 2), 2), 2), $taxable_item_amount, 2);
            $start_range = bcdiv($calculated_gst_amount, bcadd(1, bcdiv($this->allowed_difference, 100, 2), 2), 2);
            $end_range = bcmul($calculated_gst_amount, bcadd(1, bcdiv($this->allowed_difference, 100, 2), 2), 2);

            // $this->log->write("GST Rate: " . $calculated_gst_amount . " || " . $start_range . " || " . $end_range);
            if ($calculated_gst_amount == $gst_amount) {
                return $gst_rate;
            } elseif (($start_range <= $gst_amount) && ($end_range >= $gst_amount)) {
                return $gst_rate;
            }
        }

        return false;
    }
}
